# 如何让本地 localhost 支持 HTTPS 协议

> Chrome v80 版本之后，开始启用 Cookie 的 SameSite 属性，默认值为 Lax，由于项目中 ePassport 登录是通过 iframe 方式嵌入，SameSite=Lax 的默认值导致跨站 set-cookie 失败，SameSite=None 时，需要 HTTPS 访问才能生效，所以本地开发时，需要支持 HTTPS 协议

## 使用方式

1. 打开 Macbook 的钥匙串访问，点击左侧“系统”，将 rootCA.pem 拖进来
2. 双击 localhost 证书，选择“信任”下拉菜单，选择“始终信任”
3. setupProxy.js 目标地址 target 协议改为 https

## 证书生成方式

重新生成证书参考文章： https://www.freecodecamp.org/news/how-to-get-https-working-on-your-local-development-environment-in-5-minutes-7af615770eec/

## 证书过期续签

续签所需文件在 `./generate-certificate` 文件夹内

### 1. 创建根 SSL 证书

根据私钥 `root-ca.key` 生成根 SSL 证书 `root-ca.pem`

```shell
openssl req -x509 -new -nodes -key rootCA.key -sha256 -days 9999 -out rootCA.pem
```

上面命令中 `-days` 后的 9999，就是证书有效期天数

执行命令后，会提示输入密码，密码是`root`

### 2. 信任证书

方式同上方**使用方式**

### 3. 生成 localhost SSL 凭证

依次执行下面 2 条命令，生成 `localhost.key` 和 `localhost.crt`

```shell
openssl req -new -sha256 -nodes -out localhost.csr -newkey rsa:2048 -keyout localhost.key -config <( cat localhost.csr.cnf )

openssl x509 -req -in localhost.csr -CA rootCA.pem -CAkey rootCA.key -CAcreateserial -out localhost.crt -days 825 -sha256 -extfile v3.ext

```

执行第二条命令需要输入密码 `root`
上面第二条命令中 `-days` 后的 825 localhost SSL 凭证有效期天数，不要超过 825 天，否则会报“有效期过长”的错误

将生成的 `localhost.crt` `localhost.key` `rootCA.pem` 复制到 ssl 文件夹下
