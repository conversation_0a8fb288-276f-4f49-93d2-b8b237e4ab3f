NODE_ENV=development
PORT=3000
DANGEROUSLY_DISABLE_HOST_CHECK=true

APP_ENV=development

# epassport 测试环境配置字段
APP_EPASSPORT_ENV=dev
APP_ECOM_HOST=http://ecom.meishi.test.meituan.com

APP_EPASSPORT_HOST=http://fepassport.sjst.test.sankuai.com
APP_SSO_HOST=//ssodemo.it.test.sankuai.com


# 🐞REPLACE🐞 sso配置字段, 使用请替换业务自己的
# 📖DOC📖 申请clientId：https://km.sankuai.com/page/*********

APP_SSO_CLIENT_ID=2893258a16
APP_SSO_ENV=test

# 水印映射地址，线下环境
WATERMARK_HOST=http://ecom.meishi.test.meituan.com

# elink（同owl/camel），已经默认生成并注入
APP_KEY=undefined

VUE_APP_KEY=undefined