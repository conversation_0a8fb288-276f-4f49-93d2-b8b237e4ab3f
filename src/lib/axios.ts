import axios from 'axios'
import axiosWrapper, { Options } from '@nibfe/axios-wrapper'
// import { epassportInterceptor } from '@/lib/epassport'
// import { ssoInterceptor } from '@/lib/sso'

const axiosWrapperConfig: Options = {
  reportLog: true,
}

const baseConfig = {
  timeout: 200000,
  withCredentials: true,
  headers: {
    'x-requested-with': 'XMLHttpRequest',
  },
}

const instance = axios.create(baseConfig)

// 添加epassport的响应拦截器，若未鉴权会直接跳转到登录页面
// instance.interceptors.response.use(epassportInterceptor)

// 添加sso的响应拦截器，若未鉴权会直接跳转到登录页面
// instance.interceptors.response.use(ssoInterceptor)

export default axiosWrapper(instance as any, axiosWrapperConfig)
