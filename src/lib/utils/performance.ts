/**
 * 性能优化工具函数
 * 用于检测设备性能并提供相应的优化策略
 */

// 常量定义
const DEFAULT_MEMORY_GB = 4
const MEMORY_TEST_ARRAY_SIZE = 1000000
const BYTES_TO_KB = 1024
const BYTES_TO_GB_DIVISOR = BYTES_TO_KB * BYTES_TO_KB * BYTES_TO_KB
const RADIX_DECIMAL = 10
const MAC_CATALINA_MAJOR = 10
const MAC_CATALINA_MINOR = 15
const CHROME_MIN_VERSION = 70
const MAGIC_NUMBER_2 = 2

// 内存信息类型
export interface MemoryInfo {
  reported: number
  estimated: number
  confidence: string
}

// 性能检测结果类型
export interface PerformanceInfo {
  isLowPerformance: boolean
  hasHardwareAcceleration: boolean
  deviceMemory: MemoryInfo
  hardwareConcurrency: number
  connectionType: string
  shouldUseSimpleAnimations: boolean
  shouldReduceEffects: boolean
  shouldOptimizeCharts: boolean
}

// 检测硬件加速支持
export const checkHardwareAcceleration = (): boolean => {
  try {
    // 检测CSS transform3d支持
    const testElement = document.createElement('div')
    testElement.style.transform = 'translateZ(0)'
    const hasTransform3d = testElement.style.transform !== ''

    // 检测WebGL支持
    const canvas = document.createElement('canvas')
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
    const hasWebGL = !!gl

    // 检测GPU信息
    let hasDiscreteGPU = false
    if (gl && 'getExtension' in gl) {
      const webglContext = gl as WebGLRenderingContext
      const debugInfo = webglContext.getExtension('WEBGL_debug_renderer_info')
      if (debugInfo) {
        const renderer = webglContext.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)
        // 检测是否为集成显卡（通常性能较低）
        hasDiscreteGPU = !/(Intel|integrated|software)/i.test(renderer)
      }
    }

    return hasTransform3d && hasWebGL && hasDiscreteGPU
  } catch (error) {
    // eslint-disable-next-line no-console
    console.warn('Hardware acceleration detection failed:', error)
    return false
  }
}

// WebGL内存测试
const testWebGLMemory = (): number => {
  try {
    const canvas = document.createElement('canvas')
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')

    const LOW_MEMORY_SCORE = 1
    const MEDIUM_MEMORY_SCORE = 2
    const HIGH_MEMORY_SCORE = 3
    const DISCRETE_GPU_SCORE = 4
    const TEXTURE_SIZE = 2048

    if (!gl) return LOW_MEMORY_SCORE

    const webglContext = gl as WebGLRenderingContext

    // 获取WebGL内存信息
    const debugInfo = webglContext.getExtension('WEBGL_debug_renderer_info')
    if (debugInfo) {
      const renderer = webglContext.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)

      // 根据GPU型号估算显存（间接反映系统内存等级）
      if (/(RTX|GTX|Radeon|RX).*[3-9][0-9]{2,}/i.test(renderer)) {
        return DISCRETE_GPU_SCORE // 独立显卡，通常配备较多内存
      }
      if (/(Intel|integrated)/i.test(renderer)) {
        return LOW_MEMORY_SCORE // 集成显卡，共享系统内存
      }
    }

    // 测试纹理创建能力
    const texture = webglContext.createTexture()
    webglContext.bindTexture(webglContext.TEXTURE_2D, texture)

    try {
      // 尝试创建大纹理
      webglContext.texImage2D(
        webglContext.TEXTURE_2D,
        0,
        webglContext.RGBA,
        TEXTURE_SIZE,
        TEXTURE_SIZE,
        0,
        webglContext.RGBA,
        webglContext.UNSIGNED_BYTE,
        null,
      )

      if (webglContext.getError() === webglContext.NO_ERROR) {
        return HIGH_MEMORY_SCORE // 能创建大纹理，内存较充足
      }
    } catch (e) {
      // 创建失败，内存可能较少
    }

    webglContext.deleteTexture(texture)
    return MEDIUM_MEMORY_SCORE
  } catch (error) {
    return 1
  }
}

// 通过性能测试估算内存大小
const estimateMemoryFromPerformance = (): number => {
  try {
    // 测试1：创建大数组测试内存分配速度
    const start = performance.now()
    const testArray = new Array(MEMORY_TEST_ARRAY_SIZE)
      .fill(0)
      .map((_, i) => ({ id: i, data: Math.random() }))
    const arrayTime = performance.now() - start

    // 测试2：检查可用的堆内存
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const memoryInfo = (performance as any).memory
    let heapLimit = 0
    if (memoryInfo) {
      heapLimit = memoryInfo.jsHeapSizeLimit / BYTES_TO_GB_DIVISOR // 转换为GB
    }

    // 测试3：WebGL内存测试
    const webglMemory = testWebGLMemory()

    // 清理测试数据
    testArray.length = 0

    // 基于测试结果估算内存
    let estimatedMemory = DEFAULT_MEMORY_GB // 默认值

    const HEAP_MEMORY_MULTIPLIER = 3
    const FAST_ALLOCATION_TIME = 50
    const MEDIUM_ALLOCATION_TIME = 100
    const HIGH_MEMORY_GB = 16
    const MEDIUM_MEMORY_GB = 8

    const MIN_MEMORY_GB = 2
    const MAX_MEMORY_GB = 64

    if (heapLimit > 0) {
      // Chrome通常分配系统内存的25-50%给JS堆
      estimatedMemory = Math.round(heapLimit * HEAP_MEMORY_MULTIPLIER) // 估算总内存
    } else if (arrayTime < FAST_ALLOCATION_TIME) {
      // 数组创建很快，可能是高内存设备
      estimatedMemory = webglMemory > MAGIC_NUMBER_2 ? HIGH_MEMORY_GB : MEDIUM_MEMORY_GB
    } else if (arrayTime < MEDIUM_ALLOCATION_TIME) {
      estimatedMemory = MEDIUM_MEMORY_GB
    } else {
      estimatedMemory = DEFAULT_MEMORY_GB
    }

    // 限制在合理范围内
    return Math.min(Math.max(estimatedMemory, MIN_MEMORY_GB), MAX_MEMORY_GB)
  } catch (error) {
    // eslint-disable-next-line no-console
    console.warn('Memory estimation failed:', error)
    return DEFAULT_MEMORY_GB
  }
}

// 检测设备内存（改进版本）
export const getDeviceMemory = (): MemoryInfo => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const reportedMemory = (navigator as any).deviceMemory || DEFAULT_MEMORY_GB

  // 通过性能测试估算实际内存
  const estimatedMemory = estimateMemoryFromPerformance()

  // 判断置信度
  let confidence = 'low'
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  if ((navigator as any).deviceMemory) {
    confidence = 'medium' // API支持但可能不准确
  }

  return {
    reported: reportedMemory,
    estimated: estimatedMemory,
    confidence,
  }
}

// 检测CPU核心数
export const getHardwareConcurrency = (): number => {
  const DEFAULT_CPU_CORES = 4
  return navigator.hardwareConcurrency || DEFAULT_CPU_CORES
}

// 检测网络连接类型
export const getConnectionType = (): string => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const connection =
    (navigator as any).connection ||
    (navigator as any).mozConnection ||
    (navigator as any).webkitConnection
  return connection?.effectiveType || 'unknown'
}

// 检测是否为低性能设备
export const isLowPerformanceDevice = (): boolean => {
  const memoryInfo = getDeviceMemory()
  const cores = getHardwareConcurrency()
  const hasHardwareAccel = checkHardwareAcceleration()

  // 使用估算的内存值进行判断，如果估算值不可靠则使用报告值
  const effectiveMemory =
    memoryInfo.confidence === 'low' ? memoryInfo.reported : memoryInfo.estimated

  const LOW_MEMORY_THRESHOLD = 4
  const LOW_CPU_THRESHOLD = 4

  // 判断条件：内存小于4GB 或 CPU核心数小于4 或 无硬件加速
  return effectiveMemory < LOW_MEMORY_THRESHOLD || cores < LOW_CPU_THRESHOLD || !hasHardwareAccel
}

// 获取完整的性能信息
export const getPerformanceInfo = (): PerformanceInfo => {
  const hasHardwareAcceleration = checkHardwareAcceleration()
  const deviceMemory = getDeviceMemory()
  const hardwareConcurrency = getHardwareConcurrency()
  const connectionType = getConnectionType()
  const isLowPerformance = isLowPerformanceDevice()

  // 使用有效内存值进行图表优化判断
  const effectiveMemory =
    deviceMemory.confidence === 'low' ? deviceMemory.reported : deviceMemory.estimated

  const CHART_OPTIMIZATION_THRESHOLD = 8

  return {
    isLowPerformance,
    hasHardwareAcceleration,
    deviceMemory,
    hardwareConcurrency,
    connectionType,
    shouldUseSimpleAnimations: isLowPerformance || !hasHardwareAcceleration,
    shouldReduceEffects: isLowPerformance,
    shouldOptimizeCharts: isLowPerformance || effectiveMemory < CHART_OPTIMIZATION_THRESHOLD,
  }
}

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number,
): ((...args: Parameters<T>) => void) => {
  let timeout: number | null = null

  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = window.setTimeout(() => func(...args), wait)
  }
}

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number,
): ((...args: Parameters<T>) => void) => {
  let inThrottle = false

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => {
        inThrottle = false
      }, limit)
    }
  }
}

// 延迟执行函数
export const defer = (callback: () => void): void => {
  if (window.requestIdleCallback) {
    window.requestIdleCallback(callback)
  } else {
    setTimeout(callback, 0)
  }
}

// 检测是否为19款Mac（基于用户代理字符串的启发式检测）
export const isMac2019 = (): boolean => {
  const userAgent = navigator.userAgent
  const isMac = /Mac OS X/.test(userAgent)

  if (!isMac) return false

  // 检测Chrome版本和Mac版本的组合来推断可能的19款Mac
  const chromeMatch = userAgent.match(/Chrome\/(\d+)/)
  const macVersionMatch = userAgent.match(/Mac OS X (\d+)_(\d+)/)

  if (chromeMatch && macVersionMatch) {
    const chromeVersion = parseInt(chromeMatch[1], RADIX_DECIMAL)
    const macMajor = parseInt(macVersionMatch[1], RADIX_DECIMAL)
    const macMinor = parseInt(macVersionMatch[2], RADIX_DECIMAL)

    // 19款Mac通常运行macOS 10.15 (Catalina)或更高版本
    // 但可能存在Chrome版本较新但硬件加速未启用的情况
    return (
      macMajor === MAC_CATALINA_MAJOR &&
      macMinor >= MAC_CATALINA_MINOR &&
      chromeVersion >= CHROME_MIN_VERSION
    )
  }

  return false
}

// 性能监控函数
export const measurePerformance = (name: string, fn: () => void): number => {
  const start = performance.now()
  fn()
  const end = performance.now()
  const duration = end - start

  const FRAME_TIME_THRESHOLD = 16 // 60fps
  if (duration > FRAME_TIME_THRESHOLD) {
    // 超过一帧的时间（60fps）
    // eslint-disable-next-line no-console
    console.warn(`Performance warning: ${name} took ${duration.toFixed(MAGIC_NUMBER_2)}ms`)
  }

  return duration
}

// 内存使用监控
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const getMemoryUsage = (): any => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const memoryInfo = (performance as any).memory
  if (memoryInfo) {
    const BYTES_TO_MB = 1048576
    return {
      used: Math.round(memoryInfo.usedJSHeapSize / BYTES_TO_MB), // MB
      total: Math.round(memoryInfo.totalJSHeapSize / BYTES_TO_MB), // MB
      limit: Math.round(memoryInfo.jsHeapSizeLimit / BYTES_TO_MB), // MB
    }
  }
  return null
}

// 导出性能优化配置
export const getOptimizationConfig = (): {
  echartsConfig: {
    animation: boolean
    animationDuration: number
    animationEasing: 'linear' | 'cubicOut'
    progressive: number
    progressiveThreshold: number
  }
  tableConfig: {
    virtual: boolean
    pageSize: number
    showSorterTooltip: boolean
  }
  animationConfig: {
    duration: number
    easing: string
    useTransform3d: boolean
  }
} => {
  const perfInfo = getPerformanceInfo()

  const ANIMATION_DURATION_SIMPLE = 300
  const ANIMATION_DURATION_FULL = 1000
  const PROGRESSIVE_THRESHOLD_LOW = 100
  const PROGRESSIVE_THRESHOLD_HIGH = 500
  const PROGRESSIVE_THRESHOLD_NORMAL = 3000
  const TABLE_PAGE_SIZE_LOW = 10
  const TABLE_PAGE_SIZE_NORMAL = 20
  const CSS_ANIMATION_DURATION_SIMPLE = 200
  const CSS_ANIMATION_DURATION_FULL = 300

  return {
    // ECharts优化配置
    echartsConfig: {
      animation: !perfInfo.shouldReduceEffects,
      animationDuration: perfInfo.shouldUseSimpleAnimations
        ? ANIMATION_DURATION_SIMPLE
        : ANIMATION_DURATION_FULL,
      animationEasing: perfInfo.shouldUseSimpleAnimations
        ? ('linear' as const)
        : ('cubicOut' as const),
      progressive: perfInfo.shouldOptimizeCharts ? PROGRESSIVE_THRESHOLD_LOW : 0,
      progressiveThreshold: perfInfo.shouldOptimizeCharts
        ? PROGRESSIVE_THRESHOLD_HIGH
        : PROGRESSIVE_THRESHOLD_NORMAL,
    },

    // 表格优化配置
    tableConfig: {
      virtual: perfInfo.shouldOptimizeCharts,
      pageSize: perfInfo.isLowPerformance ? TABLE_PAGE_SIZE_LOW : TABLE_PAGE_SIZE_NORMAL,
      showSorterTooltip: !perfInfo.isLowPerformance,
    },

    // 动画优化配置
    animationConfig: {
      duration: perfInfo.shouldUseSimpleAnimations
        ? CSS_ANIMATION_DURATION_SIMPLE
        : CSS_ANIMATION_DURATION_FULL,
      easing: perfInfo.shouldUseSimpleAnimations ? 'ease' : 'cubic-bezier(0.4, 0, 0.2, 1)',
      useTransform3d: perfInfo.hasHardwareAcceleration,
    },
  }
}
