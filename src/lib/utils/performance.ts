/**
 * 性能优化工具函数
 * 用于检测设备性能并提供相应的优化策略
 */

// 性能检测结果类型
export interface PerformanceInfo {
  isLowPerformance: boolean
  hasHardwareAcceleration: boolean
  deviceMemory: number
  hardwareConcurrency: number
  connectionType: string
  shouldUseSimpleAnimations: boolean
  shouldReduceEffects: boolean
  shouldOptimizeCharts: boolean
}

// 检测硬件加速支持
export const checkHardwareAcceleration = (): boolean => {
  try {
    // 检测CSS transform3d支持
    const testElement = document.createElement('div')
    testElement.style.transform = 'translateZ(0)'
    const hasTransform3d = testElement.style.transform !== ''

    // 检测WebGL支持
    const canvas = document.createElement('canvas')
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
    const hasWebGL = !!gl

    // 检测GPU信息
    let hasDiscreteGPU = false
    if (gl && 'getExtension' in gl) {
      const webglContext = gl as WebGLRenderingContext
      const debugInfo = webglContext.getExtension('WEBGL_debug_renderer_info')
      if (debugInfo) {
        const renderer = webglContext.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)
        // 检测是否为集成显卡（通常性能较低）
        hasDiscreteGPU = !/(Intel|integrated|software)/i.test(renderer)
      }
    }

    return hasTransform3d && hasWebGL && hasDiscreteGPU
  } catch (error) {
    console.warn('Hardware acceleration detection failed:', error)
    return false
  }
}

// 检测设备内存
export const getDeviceMemory = (): number => {
  // @ts-ignore - navigator.deviceMemory 是实验性API
  return navigator.deviceMemory || 4 // 默认4GB
}

// 检测CPU核心数
export const getHardwareConcurrency = (): number => {
  return navigator.hardwareConcurrency || 4 // 默认4核
}

// 检测网络连接类型
export const getConnectionType = (): string => {
  // @ts-ignore - navigator.connection 是实验性API
  const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection
  return connection?.effectiveType || 'unknown'
}

// 检测是否为低性能设备
export const isLowPerformanceDevice = (): boolean => {
  const memory = getDeviceMemory()
  const cores = getHardwareConcurrency()
  const hasHardwareAccel = checkHardwareAcceleration()

  // 判断条件：内存小于4GB 或 CPU核心数小于4 或 无硬件加速
  return memory < 4 || cores < 4 || !hasHardwareAccel
}

// 获取完整的性能信息
export const getPerformanceInfo = (): PerformanceInfo => {
  const hasHardwareAcceleration = checkHardwareAcceleration()
  const deviceMemory = getDeviceMemory()
  const hardwareConcurrency = getHardwareConcurrency()
  const connectionType = getConnectionType()
  const isLowPerformance = isLowPerformanceDevice()

  return {
    isLowPerformance,
    hasHardwareAcceleration,
    deviceMemory,
    hardwareConcurrency,
    connectionType,
    shouldUseSimpleAnimations: isLowPerformance || !hasHardwareAcceleration,
    shouldReduceEffects: isLowPerformance,
    shouldOptimizeCharts: isLowPerformance || deviceMemory < 8,
  }
}

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: number | null = null

  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = window.setTimeout(() => func(...args), wait)
  }
}

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle = false

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => {
        inThrottle = false
      }, limit)
    }
  }
}

// 延迟执行函数
export const defer = (callback: () => void): void => {
  if (window.requestIdleCallback) {
    window.requestIdleCallback(callback)
  } else {
    setTimeout(callback, 0)
  }
}

// 检测是否为19款Mac（基于用户代理字符串的启发式检测）
export const isMac2019 = (): boolean => {
  const userAgent = navigator.userAgent
  const isMac = /Mac OS X/.test(userAgent)

  if (!isMac) return false

  // 检测Chrome版本和Mac版本的组合来推断可能的19款Mac
  const chromeMatch = userAgent.match(/Chrome\/(\d+)/)
  const macVersionMatch = userAgent.match(/Mac OS X (\d+)_(\d+)/)

  if (chromeMatch && macVersionMatch) {
    const chromeVersion = parseInt(chromeMatch[1])
    const macMajor = parseInt(macVersionMatch[1])
    const macMinor = parseInt(macVersionMatch[2])

    // 19款Mac通常运行macOS 10.15 (Catalina)或更高版本
    // 但可能存在Chrome版本较新但硬件加速未启用的情况
    return macMajor === 10 && macMinor >= 15 && chromeVersion >= 70
  }

  return false
}

// 性能监控函数
export const measurePerformance = (name: string, fn: () => void): number => {
  const start = performance.now()
  fn()
  const end = performance.now()
  const duration = end - start

  if (duration > 16) { // 超过一帧的时间（60fps）
    console.warn(`Performance warning: ${name} took ${duration.toFixed(2)}ms`)
  }

  return duration
}

// 内存使用监控
export const getMemoryUsage = (): any => {
  // @ts-ignore - performance.memory 是Chrome特有的API
  if (performance.memory) {
    // @ts-ignore
    return {
      // @ts-ignore
      used: Math.round(performance.memory.usedJSHeapSize / 1048576), // MB
      // @ts-ignore
      total: Math.round(performance.memory.totalJSHeapSize / 1048576), // MB
      // @ts-ignore
      limit: Math.round(performance.memory.jsHeapSizeLimit / 1048576), // MB
    }
  }
  return null
}

// 导出性能优化配置
export const getOptimizationConfig = () => {
  const perfInfo = getPerformanceInfo()

  return {
    // ECharts优化配置
    echartsConfig: {
      animation: !perfInfo.shouldReduceEffects,
      animationDuration: perfInfo.shouldUseSimpleAnimations ? 300 : 1000,
      animationEasing: perfInfo.shouldUseSimpleAnimations ? 'linear' as const : 'cubicOut' as const,
      progressive: perfInfo.shouldOptimizeCharts ? 100 : 0,
      progressiveThreshold: perfInfo.shouldOptimizeCharts ? 500 : 3000,
    },

    // 表格优化配置
    tableConfig: {
      virtual: perfInfo.shouldOptimizeCharts,
      pageSize: perfInfo.isLowPerformance ? 10 : 20,
      showSorterTooltip: !perfInfo.isLowPerformance,
    },

    // 动画优化配置
    animationConfig: {
      duration: perfInfo.shouldUseSimpleAnimations ? 200 : 300,
      easing: perfInfo.shouldUseSimpleAnimations ? 'ease' : 'cubic-bezier(0.4, 0, 0.2, 1)',
      useTransform3d: perfInfo.hasHardwareAcceleration,
    },
  }
}
