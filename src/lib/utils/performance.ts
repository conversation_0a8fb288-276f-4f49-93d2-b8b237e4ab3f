/**
 * 性能优化工具函数
 * 用于检测设备性能并提供相应的优化策略
 */

// 内存信息类型
export interface MemoryInfo {
  reported: number
  estimated: number
  confidence: string
}

// 性能检测结果类型
export interface PerformanceInfo {
  isLowPerformance: boolean
  hasHardwareAcceleration: boolean
  deviceMemory: MemoryInfo
  hardwareConcurrency: number
  connectionType: string
  shouldUseSimpleAnimations: boolean
  shouldReduceEffects: boolean
  shouldOptimizeCharts: boolean
}

// 检测硬件加速支持
export const checkHardwareAcceleration = (): boolean => {
  try {
    // 检测CSS transform3d支持
    const testElement = document.createElement('div')
    testElement.style.transform = 'translateZ(0)'
    const hasTransform3d = testElement.style.transform !== ''

    // 检测WebGL支持
    const canvas = document.createElement('canvas')
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
    const hasWebGL = !!gl

    // 检测GPU信息
    let hasDiscreteGPU = false
    if (gl && 'getExtension' in gl) {
      const webglContext = gl as WebGLRenderingContext
      const debugInfo = webglContext.getExtension('WEBGL_debug_renderer_info')
      if (debugInfo) {
        const renderer = webglContext.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)
        // 检测是否为集成显卡（通常性能较低）
        hasDiscreteGPU = !/(Intel|integrated|software)/i.test(renderer)
      }
    }

    return hasTransform3d && hasWebGL && hasDiscreteGPU
  } catch (error) {
    console.warn('Hardware acceleration detection failed:', error)
    return false
  }
}

// 检测设备内存（改进版本）
export const getDeviceMemory = (): MemoryInfo => {
  // @ts-ignore - navigator.deviceMemory 是实验性API
  const reportedMemory = navigator.deviceMemory || 4

  // 通过性能测试估算实际内存
  const estimatedMemory = estimateMemoryFromPerformance()

  // 判断置信度
  let confidence = 'low'
  // @ts-ignore - navigator.deviceMemory 是实验性API
  if (navigator.deviceMemory) {
    confidence = 'medium' // API支持但可能不准确
  }

  return {
    reported: reportedMemory,
    estimated: estimatedMemory,
    confidence
  }
}

// 通过性能测试估算内存大小
const estimateMemoryFromPerformance = (): number => {
  try {
    // 测试1：创建大数组测试内存分配速度
    const start = performance.now()
    const testArray = new Array(1000000).fill(0).map((_, i) => ({ id: i, data: Math.random() }))
    const arrayTime = performance.now() - start

    // 测试2：检查可用的堆内存
    // @ts-ignore
    const memoryInfo = performance.memory
    let heapLimit = 0
    if (memoryInfo) {
      // @ts-ignore
      heapLimit = memoryInfo.jsHeapSizeLimit / (1024 * 1024 * 1024) // 转换为GB
    }

    // 测试3：WebGL内存测试
    const webglMemory = testWebGLMemory()

    // 清理测试数据
    testArray.length = 0

    // 基于测试结果估算内存
    let estimatedMemory = 4 // 默认值

    if (heapLimit > 0) {
      // Chrome通常分配系统内存的25-50%给JS堆
      estimatedMemory = Math.round(heapLimit * 3) // 估算总内存
    } else if (arrayTime < 50) {
      // 数组创建很快，可能是高内存设备
      estimatedMemory = webglMemory > 2 ? 16 : 8
    } else if (arrayTime < 100) {
      estimatedMemory = 8
    } else {
      estimatedMemory = 4
    }

    // 限制在合理范围内
    return Math.min(Math.max(estimatedMemory, 2), 64)

  } catch (error) {
    console.warn('Memory estimation failed:', error)
    return 4
  }
}

// WebGL内存测试
const testWebGLMemory = (): number => {
  try {
    const canvas = document.createElement('canvas')
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')

    if (!gl) return 1

    const webglContext = gl as WebGLRenderingContext

    // 获取WebGL内存信息
    const debugInfo = webglContext.getExtension('WEBGL_debug_renderer_info')
    if (debugInfo) {
      const renderer = webglContext.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)

      // 根据GPU型号估算显存（间接反映系统内存等级）
      if (/(RTX|GTX|Radeon|RX).*[3-9][0-9]{2,}/i.test(renderer)) {
        return 4 // 独立显卡，通常配备较多内存
      } else if (/(Intel|integrated)/i.test(renderer)) {
        return 1 // 集成显卡，共享系统内存
      }
    }

    // 测试纹理创建能力
    const texture = webglContext.createTexture()
    webglContext.bindTexture(webglContext.TEXTURE_2D, texture)

    try {
      // 尝试创建大纹理
      webglContext.texImage2D(
        webglContext.TEXTURE_2D, 0, webglContext.RGBA,
        2048, 2048, 0, webglContext.RGBA, webglContext.UNSIGNED_BYTE, null
      )

      if (webglContext.getError() === webglContext.NO_ERROR) {
        return 3 // 能创建大纹理，内存较充足
      }
    } catch (e) {
      // 创建失败，内存可能较少
    }

    webglContext.deleteTexture(texture)
    return 2

  } catch (error) {
    return 1
  }
}

// 检测CPU核心数
export const getHardwareConcurrency = (): number => {
  return navigator.hardwareConcurrency || 4 // 默认4核
}

// 检测网络连接类型
export const getConnectionType = (): string => {
  // @ts-ignore - navigator.connection 是实验性API
  const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection
  return connection?.effectiveType || 'unknown'
}

// 检测是否为低性能设备
export const isLowPerformanceDevice = (): boolean => {
  const memoryInfo = getDeviceMemory()
  const cores = getHardwareConcurrency()
  const hasHardwareAccel = checkHardwareAcceleration()

  // 使用估算的内存值进行判断，如果估算值不可靠则使用报告值
  const effectiveMemory = memoryInfo.confidence === 'low' ? memoryInfo.reported : memoryInfo.estimated

  // 判断条件：内存小于4GB 或 CPU核心数小于4 或 无硬件加速
  return effectiveMemory < 4 || cores < 4 || !hasHardwareAccel
}

// 获取完整的性能信息
export const getPerformanceInfo = (): PerformanceInfo => {
  const hasHardwareAcceleration = checkHardwareAcceleration()
  const deviceMemory = getDeviceMemory()
  const hardwareConcurrency = getHardwareConcurrency()
  const connectionType = getConnectionType()
  const isLowPerformance = isLowPerformanceDevice()

  // 使用有效内存值进行图表优化判断
  const effectiveMemory = deviceMemory.confidence === 'low' ? deviceMemory.reported : deviceMemory.estimated

  return {
    isLowPerformance,
    hasHardwareAcceleration,
    deviceMemory,
    hardwareConcurrency,
    connectionType,
    shouldUseSimpleAnimations: isLowPerformance || !hasHardwareAcceleration,
    shouldReduceEffects: isLowPerformance,
    shouldOptimizeCharts: isLowPerformance || effectiveMemory < 8,
  }
}

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: number | null = null

  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = window.setTimeout(() => func(...args), wait)
  }
}

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle = false

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => {
        inThrottle = false
      }, limit)
    }
  }
}

// 延迟执行函数
export const defer = (callback: () => void): void => {
  if (window.requestIdleCallback) {
    window.requestIdleCallback(callback)
  } else {
    setTimeout(callback, 0)
  }
}

// 检测是否为19款Mac（基于用户代理字符串的启发式检测）
export const isMac2019 = (): boolean => {
  const userAgent = navigator.userAgent
  const isMac = /Mac OS X/.test(userAgent)

  if (!isMac) return false

  // 检测Chrome版本和Mac版本的组合来推断可能的19款Mac
  const chromeMatch = userAgent.match(/Chrome\/(\d+)/)
  const macVersionMatch = userAgent.match(/Mac OS X (\d+)_(\d+)/)

  if (chromeMatch && macVersionMatch) {
    const chromeVersion = parseInt(chromeMatch[1])
    const macMajor = parseInt(macVersionMatch[1])
    const macMinor = parseInt(macVersionMatch[2])

    // 19款Mac通常运行macOS 10.15 (Catalina)或更高版本
    // 但可能存在Chrome版本较新但硬件加速未启用的情况
    return macMajor === 10 && macMinor >= 15 && chromeVersion >= 70
  }

  return false
}

// 性能监控函数
export const measurePerformance = (name: string, fn: () => void): number => {
  const start = performance.now()
  fn()
  const end = performance.now()
  const duration = end - start

  if (duration > 16) { // 超过一帧的时间（60fps）
    console.warn(`Performance warning: ${name} took ${duration.toFixed(2)}ms`)
  }

  return duration
}

// 内存使用监控
export const getMemoryUsage = (): any => {
  // @ts-ignore - performance.memory 是Chrome特有的API
  if (performance.memory) {
    // @ts-ignore
    return {
      // @ts-ignore
      used: Math.round(performance.memory.usedJSHeapSize / 1048576), // MB
      // @ts-ignore
      total: Math.round(performance.memory.totalJSHeapSize / 1048576), // MB
      // @ts-ignore
      limit: Math.round(performance.memory.jsHeapSizeLimit / 1048576), // MB
    }
  }
  return null
}

// 导出性能优化配置
export const getOptimizationConfig = () => {
  const perfInfo = getPerformanceInfo()

  return {
    // ECharts优化配置
    echartsConfig: {
      animation: !perfInfo.shouldReduceEffects,
      animationDuration: perfInfo.shouldUseSimpleAnimations ? 300 : 1000,
      animationEasing: perfInfo.shouldUseSimpleAnimations ? 'linear' as const : 'cubicOut' as const,
      progressive: perfInfo.shouldOptimizeCharts ? 100 : 0,
      progressiveThreshold: perfInfo.shouldOptimizeCharts ? 500 : 3000,
    },

    // 表格优化配置
    tableConfig: {
      virtual: perfInfo.shouldOptimizeCharts,
      pageSize: perfInfo.isLowPerformance ? 10 : 20,
      showSorterTooltip: !perfInfo.isLowPerformance,
    },

    // 动画优化配置
    animationConfig: {
      duration: perfInfo.shouldUseSimpleAnimations ? 200 : 300,
      easing: perfInfo.shouldUseSimpleAnimations ? 'ease' : 'cubic-bezier(0.4, 0, 0.2, 1)',
      useTransform3d: perfInfo.hasHardwareAcceleration,
    },
  }
}
