/**
 * 性能优化工具函数测试
 */

import {
  checkHardwareAcceleration,
  getDeviceMemory,
  getHardwareConcurrency,
  isLowPerformanceDevice,
  getPerformanceInfo,
  debounce,
  throttle,
  getOptimizationConfig,
} from '../performance'

// Mock DOM APIs
const mockCanvas = {
  getContext: jest.fn(),
}

const mockWebGLContext = {
  getExtension: jest.fn(),
  getParameter: jest.fn(),
}

// Mock navigator
Object.defineProperty(navigator, 'deviceMemory', {
  writable: true,
  value: 8,
})

Object.defineProperty(navigator, 'hardwareConcurrency', {
  writable: true,
  value: 8,
})

// Mock document.createElement
const originalCreateElement = document.createElement
document.createElement = jest.fn((tagName: string) => {
  if (tagName === 'canvas') {
    return mockCanvas as any
  }
  if (tagName === 'div') {
    return {
      style: {},
    } as any
  }
  return originalCreateElement.call(document, tagName)
})

describe('Performance Utils', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('checkHardwareAcceleration', () => {
    it('should return true when hardware acceleration is supported', () => {
      mockCanvas.getContext.mockReturnValue(mockWebGLContext)
      mockWebGLContext.getExtension.mockReturnValue({
        UNMASKED_RENDERER_WEBGL: 'NVIDIA GeForce GTX 1080',
      })
      mockWebGLContext.getParameter.mockReturnValue('NVIDIA GeForce GTX 1080')

      const result = checkHardwareAcceleration()
      expect(result).toBe(true)
    })

    it('should return false when hardware acceleration is not supported', () => {
      mockCanvas.getContext.mockReturnValue(null)

      const result = checkHardwareAcceleration()
      expect(result).toBe(false)
    })

    it('should return false for integrated graphics', () => {
      mockCanvas.getContext.mockReturnValue(mockWebGLContext)
      mockWebGLContext.getExtension.mockReturnValue({
        UNMASKED_RENDERER_WEBGL: 'Intel Integrated Graphics',
      })
      mockWebGLContext.getParameter.mockReturnValue('Intel Integrated Graphics')

      const result = checkHardwareAcceleration()
      expect(result).toBe(false)
    })
  })

  describe('getDeviceMemory', () => {
    it('should return device memory when available', () => {
      const result = getDeviceMemory()
      expect(result).toBe(8)
    })

    it('should return default value when not available', () => {
      Object.defineProperty(navigator, 'deviceMemory', {
        value: undefined,
      })

      const result = getDeviceMemory()
      expect(result).toBe(4)
    })
  })

  describe('getHardwareConcurrency', () => {
    it('should return hardware concurrency when available', () => {
      const result = getHardwareConcurrency()
      expect(result).toBe(8)
    })
  })

  describe('isLowPerformanceDevice', () => {
    it('should return false for high-performance device', () => {
      mockCanvas.getContext.mockReturnValue(mockWebGLContext)
      mockWebGLContext.getExtension.mockReturnValue({
        UNMASKED_RENDERER_WEBGL: 'NVIDIA GeForce GTX 1080',
      })
      mockWebGLContext.getParameter.mockReturnValue('NVIDIA GeForce GTX 1080')

      const result = isLowPerformanceDevice()
      expect(result).toBe(false)
    })

    it('should return true for low-memory device', () => {
      Object.defineProperty(navigator, 'deviceMemory', {
        value: 2,
      })

      const result = isLowPerformanceDevice()
      expect(result).toBe(true)
    })
  })

  describe('getPerformanceInfo', () => {
    it('should return complete performance info', () => {
      const result = getPerformanceInfo()
      
      expect(result).toHaveProperty('isLowPerformance')
      expect(result).toHaveProperty('hasHardwareAcceleration')
      expect(result).toHaveProperty('deviceMemory')
      expect(result).toHaveProperty('hardwareConcurrency')
      expect(result).toHaveProperty('shouldUseSimpleAnimations')
      expect(result).toHaveProperty('shouldReduceEffects')
      expect(result).toHaveProperty('shouldOptimizeCharts')
    })
  })

  describe('debounce', () => {
    it('should debounce function calls', (done) => {
      const mockFn = jest.fn()
      const debouncedFn = debounce(mockFn, 100)

      debouncedFn()
      debouncedFn()
      debouncedFn()

      expect(mockFn).not.toHaveBeenCalled()

      setTimeout(() => {
        expect(mockFn).toHaveBeenCalledTimes(1)
        done()
      }, 150)
    })
  })

  describe('throttle', () => {
    it('should throttle function calls', (done) => {
      const mockFn = jest.fn()
      const throttledFn = throttle(mockFn, 100)

      throttledFn()
      throttledFn()
      throttledFn()

      expect(mockFn).toHaveBeenCalledTimes(1)

      setTimeout(() => {
        throttledFn()
        expect(mockFn).toHaveBeenCalledTimes(2)
        done()
      }, 150)
    })
  })

  describe('getOptimizationConfig', () => {
    it('should return optimization config', () => {
      const config = getOptimizationConfig()

      expect(config).toHaveProperty('echartsConfig')
      expect(config).toHaveProperty('tableConfig')
      expect(config).toHaveProperty('animationConfig')

      expect(config.echartsConfig).toHaveProperty('animation')
      expect(config.echartsConfig).toHaveProperty('animationDuration')
      expect(config.echartsConfig).toHaveProperty('animationEasing')

      expect(config.tableConfig).toHaveProperty('virtual')
      expect(config.tableConfig).toHaveProperty('pageSize')

      expect(config.animationConfig).toHaveProperty('duration')
      expect(config.animationConfig).toHaveProperty('easing')
    })
  })
})

// Cleanup
afterAll(() => {
  document.createElement = originalCreateElement
})
