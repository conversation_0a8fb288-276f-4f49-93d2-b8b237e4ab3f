import axios, { AxiosRequestConfig, AxiosResponse } from 'axios'
import { message } from 'antd'
import { API_BASE_CONFIG } from '@/lib/config/api'

// 创建axios实例
const request = axios.create({
  timeout: API_BASE_CONFIG.timeout,
  headers: API_BASE_CONFIG.headers,
  withCredentials: false, // 跨域请求时不发送凭证
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 可以在这里添加token等认证信息
    // eslint-disable-next-line no-console
    console.log('发送请求:', config.method?.toUpperCase(), config.url)
    return config
  },
  error => {
    // eslint-disable-next-line no-console
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  },
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    // eslint-disable-next-line no-console
    console.log('请求成功:', response.status, response.config.url)
    return response
  },
  error => {
    // 统一错误处理
    // eslint-disable-next-line no-console
    console.error('请求失败:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      statusText: error.response?.statusText,
      message: error.message,
      code: error.code,
    })

    const errorMessage = error.response?.data?.message || error.message || '请求失败'
    message.error(`网络请求失败: ${errorMessage}`)
    return Promise.reject(error)
  },
)

// GET请求
export const get = <T = any>(
  url: string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  params?: Record<string, any>,
  config?: AxiosRequestConfig,
): Promise<T> => {
  return request.get(url, { params, ...config }).then(res => res.data)
}

// POST请求
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const post = <T = any>(
  url: string,
  data?: Record<string, any>,
  config?: AxiosRequestConfig,
): Promise<T> => {
  return request.post(url, data, config).then(res => res.data)
}

// PUT请求
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const put = <T = any>(
  url: string,
  data?: Record<string, any>,
  config?: AxiosRequestConfig,
): Promise<T> => {
  return request.put(url, data, config).then(res => res.data)
}

// DELETE请求
export const del = <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  return request.delete(url, config).then(res => res.data)
}

export default request
