import React, { useEffect, useRef, useState } from '@rome/stone/react'
import { observer } from '@rome/stone/mobx-react'
import * as echarts from 'echarts'
import {
  Card,
  Row,
  Col,
  ConfigProvider,
  Statistic,
  Select,
  DatePicker,
  Space,
  Table,
  List,
  Tag,
  Spin,
  Alert,
  Typography,
  Button,
} from 'antd'
import {
  DashboardOutlined,
  RiseOutlined,
  FallOutlined,
  CodeOutlined,
  TeamOutlined,
  FolderOutlined,
  RobotOutlined,
  SearchOutlined,
  ClearOutlined,
} from '@ant-design/icons'
import zhCN from 'antd/lib/locale/zh_CN'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import { DashboardStore } from '../../store'
import 'dayjs/locale/zh-cn'

// 配置dayjs插件
dayjs.extend(relativeTime)

const { RangePicker } = DatePicker
const { Option } = Select
const { Text, Title } = Typography

// 设置dayjs使用中文
dayjs.locale('zh-cn')

// 时间范围选项
const TIME_RANGE_OPTIONS = [
  { label: '最近7天', value: '7d' },
  { label: '最近30天', value: '30d' },
  { label: '最近90天', value: '90d' },
  { label: '自定义', value: 'custom' },
]

// 文件类型选项
const FILE_TYPE_OPTIONS = [
  // 前端相关
  { label: 'JavaScript', value: 'js' },
  { label: 'TypeScript', value: 'ts' },
  { label: 'JSX', value: 'jsx' },
  { label: 'TSX', value: 'tsx' },
  { label: 'Vue', value: 'vue' },
  { label: 'HTML', value: 'html' },
  { label: 'CSS', value: 'css' },
  { label: 'SCSS', value: 'scss' },
  { label: 'SASS', value: 'sass' },
  { label: 'Less', value: 'less' },
  { label: 'Stylus', value: 'styl' },

  // 小程序相关
  { label: 'WXML', value: 'wxml' },
  { label: 'WXSS', value: 'wxss' },
  { label: 'WXS', value: 'wxs' },
  { label: 'AXML', value: 'axml' },
  { label: 'ACSS', value: 'acss' },
  { label: 'SJS', value: 'sjs' },

  // 后端语言 + 配置文件 + 其他
  // ... 总共40+种文件类型
]

// 活动类型映射
const ACTIVITY_TYPE_MAP: Record<string, { text: string; icon: string; color: string }> = {
  analysis_completed: { text: 'PR分析完成', icon: '🔍', color: '#52c41a' },
  repo_analyzed: { text: '仓库分析', icon: '📊', color: '#1890ff' },
  high_ai_ratio: { text: '高AI占比代码', icon: '👤', color: '#fa8c16' },
  trend_detected: { text: '趋势检测', icon: '📈', color: '#722ed1' },
}

// 常量定义
const SPACING_24 = 24
const SPACING_8 = 8
const AI_RATIO_THRESHOLDS = {
  HIGH: 80,
  MEDIUM: 60,
  LOW: 40,
} as const

const Home: React.FC = observer(() => {
  const trendChartRef = useRef<HTMLDivElement>(null)
  const fileTypeChartRef = useRef<HTMLDivElement>(null)
  const detectionMethodChartRef = useRef<HTMLDivElement>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 加载大盘数据
  const loadDashboardData = async () => {
    try {
      setLoading(true)
      setError(null)
      await DashboardStore.getDashboardData()
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载数据失败')
    } finally {
      setLoading(false)
    }
  }

  // 初始化数据
  useEffect(() => {
    loadDashboardData()
  }, [])

  // 本地筛选状态
  const [localFilters, setLocalFilters] = useState({
    timeRange: '30d',
    startDate: '',
    endDate: '',
    repository: '',
    developer: '',
    fileType: '',
  })

  // 处理时间范围变化
  const handleTimeRangeChange = (value: string) => {
    const newFilters = {
      ...localFilters,
      timeRange: value,
    }

    // 如果不是自定义时间，清除日期
    if (value !== 'custom') {
      newFilters.startDate = ''
      newFilters.endDate = ''
    }

    setLocalFilters(newFilters)
  }

  // 处理自定义日期范围变化
  const handleDateRangeChange = (_dates: any, dateStrings: [string, string]) => {
    setLocalFilters({
      ...localFilters,
      startDate: dateStrings[0] || '',
      endDate: dateStrings[1] || '',
    })
  }

  // 处理下拉框变化
  const handleSelectChange = (field: string, value: string) => {
    setLocalFilters({
      ...localFilters,
      [field]: value || '',
    })
  }

  // 应用筛选条件
  const handleApplyFilters = () => {
    DashboardStore.setFilters(localFilters)
    loadDashboardData()
  }

  // 重置筛选条件
  const handleResetFilters = () => {
    const resetFilters = {
      timeRange: '30d',
      startDate: '',
      endDate: '',
      repository: '',
      developer: '',
      fileType: '',
    }
    setLocalFilters(resetFilters)
    DashboardStore.setFilters(resetFilters)
    loadDashboardData()
  }

  // 初始化趋势图表
  useEffect(() => {
    const trendData = DashboardStore.trendChartData
    if (trendChartRef.current && trendData) {
      const chart = echarts.init(trendChartRef.current)
      const { dates, aiRatios, prCounts } = trendData

      const option = {
        title: {
          text: 'AI代码使用趋势',
          left: 'center',
          textStyle: { fontSize: 16, fontWeight: 'normal' },
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'cross' },
        },
        legend: {
          data: ['AI占比', 'PR数量'],
          top: '10%',
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '20%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: dates,
          axisLabel: { rotate: 45 },
        },
        yAxis: [
          {
            type: 'value',
            name: 'AI占比 (%)',
            position: 'left',
            max: 100,
          },
          {
            type: 'value',
            name: 'PR数量',
            position: 'right',
          },
        ],
        series: [
          {
            name: 'AI占比',
            type: 'line',
            yAxisIndex: 0,
            data: aiRatios,
            itemStyle: { color: '#1890ff' },
            smooth: true,
          },
          {
            name: 'PR数量',
            type: 'bar',
            yAxisIndex: 1,
            data: prCounts,
            itemStyle: { color: '#52c41a' },
          },
        ],
      }

      chart.setOption(option)

      const handleResize = () => chart.resize()
      window.addEventListener('resize', handleResize)

      return () => {
        window.removeEventListener('resize', handleResize)
        chart.dispose()
      }
    }
    return undefined
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [DashboardStore.dashboardData])

  // 初始化文件类型分布图表
  useEffect(() => {
    const fileTypeData = DashboardStore.fileTypeDistribution
    if (fileTypeChartRef.current && fileTypeData.length > 0) {
      const chart = echarts.init(fileTypeChartRef.current)
      const data = fileTypeData

      const option = {
        title: {
          text: '文件类型AI使用分布',
          left: 'center',
          textStyle: { fontSize: 16, fontWeight: 'normal' },
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          formatter: (params: any) => {
            const item = params[0]
            return `${item.name}: ${item.value}% (${item.data.fileCount} files)`
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '20%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: data.map(item => item.type),
          axisLabel: { rotate: 45 },
        },
        yAxis: {
          type: 'value',
          name: 'AI占比 (%)',
          max: 100,
        },
        series: [
          {
            name: 'AI占比',
            type: 'bar',
            data: data.map(item => ({
              value: item.aiRatio,
              fileCount: item.fileCount,
            })),
            itemStyle: {
              color: (params: any) => {
                const value = params.value.value || params.value
                if (value > AI_RATIO_THRESHOLDS.HIGH) return '#52c41a'
                if (value > AI_RATIO_THRESHOLDS.MEDIUM) return '#1890ff'
                if (value > AI_RATIO_THRESHOLDS.LOW) return '#faad14'
                return '#f5222d'
              },
            },
            label: {
              show: true,
              position: 'top',
              formatter: '{c}%',
            },
          },
        ],
      }

      chart.setOption(option)

      const handleResize = () => chart.resize()
      window.addEventListener('resize', handleResize)

      return () => {
        window.removeEventListener('resize', handleResize)
        chart.dispose()
      }
    }
    return undefined
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [DashboardStore.dashboardData])

  // 初始化检测方法分布图表
  useEffect(() => {
    const detectionData = DashboardStore.detectionMethodsData
    if (detectionMethodChartRef.current && detectionData.length > 0) {
      const chart = echarts.init(detectionMethodChartRef.current)
      const data = detectionData

      const option = {
        title: {
          text: '检测方法分布',
          left: 'center',
          textStyle: { fontSize: 16, fontWeight: 'normal' },
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)',
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: data.map(item => item.method),
        },
        series: [
          {
            name: '检测方法',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '60%'],
            data: data.map(item => ({
              value: item.count,
              name: item.method,
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
            label: {
              show: true,
              formatter: '{b}: {d}%',
            },
          },
        ],
      }

      chart.setOption(option)

      const handleResize = () => chart.resize()
      window.addEventListener('resize', handleResize)

      return () => {
        window.removeEventListener('resize', handleResize)
        chart.dispose()
      }
    }
    return undefined
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [DashboardStore.dashboardData])

  // 渲染总览指标卡片
  const renderOverviewCards = () => {
    const overview = DashboardStore.formattedOverview
    if (!overview) return null

    const cards = [
      {
        title: '总PR数量',
        value: overview.totalPRs.value,
        suffix: overview.totalPRs.suffix,
        growth: overview.totalPRs.growth,
        icon: <CodeOutlined />,
        color: '#1890ff',
      },
      {
        title: 'AI代码占比',
        value: overview.avgAIRatio.value,
        suffix: overview.avgAIRatio.suffix,
        growth: overview.avgAIRatio.growth,
        icon: <RobotOutlined />,
        color: '#52c41a',
      },
      {
        title: '活跃仓库数',
        value: overview.activeRepos.value,
        suffix: overview.activeRepos.suffix,
        growth: overview.activeRepos.growth,
        icon: <FolderOutlined />,
        color: '#fa8c16',
      },
      {
        title: '活跃开发者',
        value: overview.activeDevelopers.value,
        suffix: overview.activeDevelopers.suffix,
        growth: overview.activeDevelopers.growth,
        icon: <TeamOutlined />,
        color: '#722ed1',
      },
    ]

    return (
      <Row gutter={[SPACING_24, SPACING_24]} style={{ marginBottom: SPACING_24 }}>
        {cards.map(card => (
          <Col span={6} key={card.title}>
            <Card
              style={{
                height: '140px',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
              }}
            >
              <Statistic
                title={card.title}
                value={card.value}
                suffix={card.suffix}
                prefix={card.icon}
                valueStyle={{ color: card.color }}
              />
              {card.growth !== 0 && (
                <div style={{ marginTop: SPACING_8 }}>
                  <Text type={card.growth > 0 ? 'success' : 'danger'}>
                    {card.growth > 0 ? <RiseOutlined /> : <FallOutlined />}
                    {Math.abs(card.growth)}% 本月
                  </Text>
                </div>
              )}
            </Card>
          </Col>
        ))}
      </Row>
    )
  }

  // 渲染筛选器
  const renderFilters = () => (
    <Card title="筛选条件" style={{ marginBottom: SPACING_24 }}>
      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px', alignItems: 'flex-end' }}>
        {/* 时间范围 */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
          <Text strong>时间范围:</Text>
          <Select
            value={localFilters.timeRange}
            onChange={handleTimeRangeChange}
            style={{ width: 220 }}
            placeholder="选择时间"
          >
            {TIME_RANGE_OPTIONS.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </div>

        {/* 自定义日期范围 */}
        {localFilters.timeRange === 'custom' && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
            <Text strong>自定义日期:</Text>
            <RangePicker
              onChange={handleDateRangeChange}
              format="YYYY-MM-DD"
              style={{ width: 240 }}
              placeholder={['开始日期', '结束日期']}
            />
          </div>
        )}

        {/* 仓库 */}
        {/* <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
          <Text strong>仓库:</Text>
          <Input
            value={localFilters.repository}
            onChange={e => handleInputChange('repository', e.target.value)}
            style={{ width: 300 }}
            placeholder="输入仓库名"
            allowClear
          />
        </div> */}

        {/* 开发者 */}
        {/* <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
          <Text strong>开发者:</Text>
          <Input
            value={localFilters.developer}
            onChange={e => handleInputChange('developer', e.target.value)}
            style={{ width: 150 }}
            placeholder="输入开发者"
            allowClear
          />
        </div> */}

        {/* 文件类型 */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
          <Text strong>文件类型:</Text>
          <Select
            value={localFilters.fileType}
            onChange={value => handleSelectChange('fileType', value)}
            style={{ width: 220 }}
            placeholder="选择类型"
            allowClear
          >
            {FILE_TYPE_OPTIONS.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </div>

        {/* 操作按钮 */}
        <div style={{ display: 'flex', gap: '8px' }}>
          <Button
            type="primary"
            icon={<SearchOutlined />}
            onClick={handleApplyFilters}
            loading={loading}
          >
            查询
          </Button>
          <Button icon={<ClearOutlined />} onClick={handleResetFilters}>
            重置
          </Button>
        </div>
      </div>
    </Card>
  )

  // 渲染排行榜表格
  const renderRankingTable = (title: string, data: any[], columns: any[]) => (
    <Card title={title} style={{ height: '100%' }}>
      <Table
        dataSource={data}
        columns={columns}
        pagination={{ pageSize: 10 }}
        size="small"
        rowKey={record => record.name || record.id || Math.random().toString()}
      />
    </Card>
  )

  // 渲染活动列表
  const renderActivityList = () => (
    <Card title="实时活动流" style={{ height: '100%' }}>
      <List
        dataSource={DashboardStore.recentActivities}
        renderItem={item => {
          const activityInfo = ACTIVITY_TYPE_MAP[item.type] || {
            text: '未知活动',
            icon: '❓',
            color: '#999',
          }

          return (
            <List.Item>
              <List.Item.Meta
                avatar={<div style={{ fontSize: '20px' }}>{activityInfo.icon}</div>}
                title={
                  <Space>
                    <Text strong>{activityInfo.text}</Text>
                    <Tag color={activityInfo.color}>AI占比: {item.aiRatio}%</Tag>
                  </Space>
                }
                description={
                  <div>
                    <Text type="secondary">
                      PR #{item.prId} - {item.prTitle}
                    </Text>
                    <br />
                    <Text type="secondary">
                      {item.developer} · {item.repository} · {dayjs(item.timestamp).fromNow()}
                    </Text>
                  </div>
                }
              />
            </List.Item>
          )
        }}
        pagination={{ pageSize: 5 }}
      />
    </Card>
  )

  return (
    <ConfigProvider locale={zhCN}>
      <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
        {/* 页面标题 */}
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 24 }}>
          <DashboardOutlined style={{ fontSize: '28px', color: '#667eea', marginRight: '12px' }} />
          <Title level={2} style={{ margin: 0 }}>
            AI代码分析大盘
          </Title>
        </div>

        {/* 筛选器 */}
        {renderFilters()}

        {/* 错误提示 */}
        {error && (
          <Alert
            message="数据加载失败"
            description={error}
            type="error"
            showIcon
            closable
            style={{ marginBottom: 24 }}
          />
        )}

        {/* 加载状态 */}
        <Spin spinning={loading}>
          {/* 总览指标卡片 */}
          {renderOverviewCards()}

          {/* 图表区域 */}
          <Row gutter={[SPACING_24, SPACING_24]} style={{ marginBottom: SPACING_24 }}>
            {/* 趋势图表 */}
            <Col span={16}>
              <Card title="AI代码使用趋势">
                <div ref={trendChartRef} style={{ height: '400px' }} />
              </Card>
            </Col>

            {/* 文件类型分布 */}
            <Col span={8}>
              <Card title="文件类型分布">
                <div ref={fileTypeChartRef} style={{ height: '400px' }} />
              </Card>
            </Col>
          </Row>

          {/* 排行榜和活动区域 */}
          <Row gutter={[SPACING_24, SPACING_24]} style={{ marginBottom: SPACING_24 }}>
            {/* 仓库排行榜 */}
            <Col span={8}>
              {renderRankingTable('仓库排行榜', DashboardStore.repositoryRankings, [
                { title: '仓库名', dataIndex: 'name', key: 'name' },
                {
                  title: 'AI占比',
                  dataIndex: 'aiRatio',
                  key: 'aiRatio',
                  render: (val: number) => `${val}%`,
                },
                { title: 'PR数量', dataIndex: 'prCount', key: 'prCount' },
              ])}
            </Col>

            {/* 开发者排行榜 */}
            <Col span={8}>
              {renderRankingTable('开发者排行榜', DashboardStore.developerRankings, [
                { title: '开发者', dataIndex: 'name', key: 'name' },
                { title: 'AI行数', dataIndex: 'aiLines', key: 'aiLines' },
                {
                  title: 'AI占比',
                  dataIndex: 'aiRatio',
                  key: 'aiRatio',
                  render: (val: number) => `${val}%`,
                },
              ])}
            </Col>

            {/* 实时活动流 */}
            <Col span={8}>{renderActivityList()}</Col>
          </Row>

          {/* 检测方法分布 */}
          <Row gutter={[SPACING_24, SPACING_24]}>
            <Col span={12}>
              <Card title="检测方法分布">
                <div ref={detectionMethodChartRef} style={{ height: '300px' }} />
              </Card>
            </Col>

            <Col span={12}>
              <Card title="检测方法准确率">
                <Table
                  dataSource={DashboardStore.detectionMethodsData}
                  columns={[
                    { title: '检测方法', dataIndex: 'method', key: 'method' },
                    { title: '使用次数', dataIndex: 'count', key: 'count' },
                    {
                      title: '准确率',
                      dataIndex: 'accuracy',
                      key: 'accuracy',
                      render: (val: number) => (val ? `${val}%` : '-'),
                    },
                  ]}
                  pagination={false}
                  size="small"
                  rowKey="method"
                />
              </Card>
            </Col>
          </Row>
        </Spin>
      </div>
    </ConfigProvider>
  )
})

export default Home
