import React, { useState, useEffect, useCallback, useMemo } from '@rome/stone/react'
import {
  Card,
  Table,
  Button,
  Input,
  Form,
  DatePicker,
  Row,
  Col,
  message,
  ConfigProvider,
  Progress,
  Tooltip,
  Space,
} from 'antd'
import zhCN from 'antd/lib/locale/zh_CN'
import {
  SearchOutlined,
  ReloadOutlined,
  FileTextOutlined,
  CodeOutlined,
  RobotOutlined,
} from '@ant-design/icons'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import { get } from '@/lib/utils/request'
import { API_CONFIG } from '@/lib/config/api'
import { getOptimizationConfig } from '@/lib/utils/performance'

// 设置dayjs使用中文
dayjs.locale('zh-cn')

const { RangePicker } = DatePicker

// 根据接口文档定义数据类型
/* eslint-disable camelcase */
interface FileDetail {
  id: number
  report_id: number
  pr_global_id: string
  file_name: string
  file_type: string
  total_lines: number
  changed_lines: number
  ai_matched_lines: number
  ai_code_ratio: number
  detection_method: string
  created_at: string
  updated_at: string
}

interface AnalysisRecord {
  id: number
  pr_global_id: string
  pr_title: string
  git_repository: string
  git_branch: string
  mis_number: string
  detection_method: string
  analysis_timestamp: string
  total_files: number
  total_lines: number
  changed_lines: number
  ai_generated_lines: number
  ai_code_ratio: number
  created_at: string
  updated_at: string
  fileDetails: FileDetail[]
}

interface QueryParams {
  page: number
  pageSize: number
  analysisTime?: string
  startTime?: string
  endTime?: string
  misNumber?: string
  prId?: string
  gitRepository?: string
  gitBranch?: string
}

interface ApiResponse {
  success: boolean
  message: string
  data: {
    list: AnalysisRecord[]
    pagination: {
      currentPage: number
      pageSize: number
      totalCount: number
      totalPages: number
      hasNext: boolean
      hasPrev: boolean
    }
  }
}

// 常量定义
const COL_SPAN_FORM = 6
const ROW_GUTTER_VALUE = 16

// UI常量
const PERCENTAGE_MULTIPLIER = 100
const HIGH_AI_THRESHOLD = 80
const MEDIUM_AI_THRESHOLD = 60
const LOW_AI_THRESHOLD = 40
const TEXT_TRUNCATE_LENGTH = 40
const TITLE_TRUNCATE_LENGTH = 25
const REPO_TRUNCATE_LENGTH = 25
const BRANCH_TRUNCATE_LENGTH = 20
const DECIMAL_PLACES = 2
const PROGRESS_DECIMAL_PLACES = 1
const BUTTON_MARGIN = 8
const CARD_MARGIN = 24
const TABLE_MARGIN = 16
const EXPANDED_TABLE_MARGIN = 20
const FORM_MARGIN = 8
const FONT_SIZE_TITLE = 18
const FONT_WEIGHT_TITLE = 600
const COL_SPAN_BUTTONS = 18
const TABLE_ROW_INDEX_DIVISOR = 2

const AIAnalysis: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<AnalysisRecord[]>([])
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)

  // 性能优化配置
  const optimizationConfig = useMemo(() => getOptimizationConfig(), [])
  const pageSize = optimizationConfig.tableConfig.pageSize

  // 查询表单状态
  const [queryForm, setQueryForm] = useState<Partial<QueryParams>>({
    misNumber: '',
    prId: '',
    gitRepository: '',
    gitBranch: '',
    startTime: '',
    endTime: '',
  })

  // API调用函数
  const fetchData = useCallback(async (params: QueryParams) => {
    setLoading(true)
    try {
      const result: ApiResponse = await get(API_CONFIG.AI_ANALYSIS.QUERY_ANALYSIS_RECORDS, params)

      if (result.success) {
        setData(result.data.list || [])
        setTotal(result.data.pagination.totalCount || 0)
      } else {
        message.error(result.message || '查询失败')
        setData([])
        setTotal(0)
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('获取数据失败:', error)
      message.error('获取数据失败，请检查网络连接')
      setData([])
      setTotal(0)
    } finally {
      setLoading(false)
    }
  }, [])

  // 处理查询
  const handleSearch = useCallback(() => {
    const params: QueryParams = {
      page: 1,
      pageSize,
      ...queryForm,
    }
    setCurrentPage(1)
    fetchData(params)
  }, [queryForm, fetchData, pageSize])

  // 处理重置
  const handleReset = () => {
    setQueryForm({
      misNumber: '',
      prId: '',
      gitRepository: '',
      gitBranch: '',
      startTime: '',
      endTime: '',
    })
    setCurrentPage(1)
    fetchData({
      page: 1,
      pageSize,
    })
  }

  // 处理分页
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    fetchData({
      page,
      pageSize,
      ...queryForm,
    })
  }

  // 时间范围变更处理函数
  const handleDateRangeChange = (_dates: any, dateStrings: [string, string]) => {
    setQueryForm({
      ...queryForm,
      startTime: dateStrings[0] ? `${dateStrings[0]}T00:00:00.000Z` : '',
      endTime: dateStrings[1] ? `${dateStrings[1]}T23:59:59.999Z` : '',
    })
  }

  // 获取文件类型图标
  const getFileTypeIcon = (fileType: string) => {
    const type = fileType.toLowerCase()
    if (
      type.includes('js') ||
      type.includes('ts') ||
      type.includes('javascript') ||
      type.includes('typescript')
    ) {
      return <CodeOutlined style={{ color: '#f0db4f' }} />
    }
    return <FileTextOutlined />
  }

  // 获取AI百分比颜色
  const getAIPercentageColor = (ratio: number) => {
    const percentage = ratio * PERCENTAGE_MULTIPLIER
    if (percentage >= HIGH_AI_THRESHOLD) return '#f5222d'
    if (percentage >= MEDIUM_AI_THRESHOLD) return '#fa8c16'
    if (percentage >= LOW_AI_THRESHOLD) return '#faad14'
    return '#52c41a'
  }

  // 文件详情表格列
  const fileColumns = [
    {
      title: '文件名',
      dataIndex: 'file_name',
      key: 'file_name',
      width: 300,
      render: (text: string, record: FileDetail) => (
        <Space>
          {getFileTypeIcon(record.file_type)}
          <Tooltip title={text}>
            <span style={{ wordBreak: 'break-all', whiteSpace: 'normal' }}>
              {text.length > TEXT_TRUNCATE_LENGTH
                ? `${text.substring(0, TEXT_TRUNCATE_LENGTH)}...`
                : text}
            </span>
          </Tooltip>
        </Space>
      ),
    },
    {
      title: '文件类型',
      dataIndex: 'file_type',
      key: 'file_type',
      width: 100,
    },
    {
      title: '总行数',
      dataIndex: 'total_lines',
      key: 'total_lines',
      width: 80,
    },
    {
      title: '变更行数',
      dataIndex: 'changed_lines',
      key: 'changed_lines',
      width: 80,
    },
    {
      title: 'AI匹配行数',
      dataIndex: 'ai_matched_lines',
      key: 'ai_matched_lines',
      width: 100,
    },
    {
      title: 'AI代码占比',
      dataIndex: 'ai_code_ratio',
      key: 'ai_code_ratio',
      width: 180,
      render: (ratio: number) => {
        const percentage = ratio * PERCENTAGE_MULTIPLIER
        return (
          <Tooltip title={`${percentage.toFixed(DECIMAL_PLACES)}% AI生成代码`}>
            <Progress
              percent={percentage}
              size="small"
              strokeColor={getAIPercentageColor(ratio)}
              format={percent => (percent ? `${percent.toFixed(PROGRESS_DECIMAL_PLACES)}%` : '0%')}
            />
          </Tooltip>
        )
      },
    },
    {
      title: '检测方法',
      dataIndex: 'detection_method',
      key: 'detection_method',
      width: 120,
    },
  ]

  // 定义主表格列
  const columns = [
    {
      title: 'PR ID',
      dataIndex: 'pr_global_id',
      key: 'pr_global_id',
      width: 120,
    },
    {
      title: 'PR标题',
      dataIndex: 'pr_title',
      key: 'pr_title',
      width: 200,
      render: (text: string) => (
        <Tooltip title={text}>
          <div style={{ wordBreak: 'break-all', whiteSpace: 'normal' }}>
            {text.length > TITLE_TRUNCATE_LENGTH
              ? `${text.substring(0, TITLE_TRUNCATE_LENGTH)}...`
              : text}
          </div>
        </Tooltip>
      ),
    },
    {
      title: '仓库地址',
      dataIndex: 'git_repository',
      key: 'git_repository',
      width: 200,
      render: (text: string) => (
        <Tooltip title={text}>
          <div style={{ wordBreak: 'break-all', whiteSpace: 'normal' }}>
            {text.length > REPO_TRUNCATE_LENGTH
              ? `${text.substring(0, REPO_TRUNCATE_LENGTH)}...`
              : text}
          </div>
        </Tooltip>
      ),
    },
    {
      title: '分支',
      dataIndex: 'git_branch',
      key: 'git_branch',
      width: 150,
      render: (text: string) => (
        <Tooltip title={text}>
          <div style={{ wordBreak: 'break-all', whiteSpace: 'normal' }}>
            {text.length > BRANCH_TRUNCATE_LENGTH
              ? `${text.substring(0, BRANCH_TRUNCATE_LENGTH)}...`
              : text}
          </div>
        </Tooltip>
      ),
    },
    {
      title: 'MIS号',
      dataIndex: 'mis_number',
      key: 'mis_number',
      width: 120,
    },
    {
      title: '文件数',
      dataIndex: 'total_files',
      key: 'total_files',
      width: 80,
    },
    {
      title: 'AI代码占比',
      dataIndex: 'ai_code_ratio',
      key: 'ai_code_ratio',
      width: 180,
      render: (ratio: number) => {
        const percentage = ratio * PERCENTAGE_MULTIPLIER
        return (
          <Tooltip title={`${percentage.toFixed(DECIMAL_PLACES)}% AI生成代码`}>
            <Progress
              percent={percentage}
              size="small"
              strokeColor={getAIPercentageColor(ratio)}
              format={percent => (percent ? `${percent.toFixed(PROGRESS_DECIMAL_PLACES)}%` : '0%')}
            />
          </Tooltip>
        )
      },
    },
    {
      title: '分析时间',
      dataIndex: 'analysis_timestamp',
      key: 'analysis_timestamp',
      width: 120,
      render: (timestamp: string) => dayjs(timestamp).format('YYYY-MM-DD HH:mm'),
    },
  ]

  // 初始加载
  useEffect(() => {
    fetchData({
      page: 1,
      pageSize,
    })
  }, [fetchData, pageSize])

  return (
    <ConfigProvider locale={zhCN}>
      <div style={{ minHeight: 'calc(100vh - 64px)' }}>
        <Card
          title={
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <RobotOutlined style={{ marginRight: BUTTON_MARGIN, color: '#0ea5e9' }} />
              <span
                style={{
                  fontSize: `${FONT_SIZE_TITLE}px`,
                  fontWeight: FONT_WEIGHT_TITLE,
                  color: '#334155',
                }}
              >
                AI代码分析
              </span>
            </div>
          }
          style={{
            marginBottom: `${CARD_MARGIN}px`,
          }}
        >
          <Form layout="inline" style={{ marginBottom: `${CARD_MARGIN}px` }}>
            <Row gutter={[ROW_GUTTER_VALUE, ROW_GUTTER_VALUE]} style={{ width: '100%' }}>
              <Col span={COL_SPAN_FORM}>
                <Form.Item
                  label="时间范围"
                  style={{ width: '100%', marginBottom: `${FORM_MARGIN}px` }}
                >
                  <RangePicker
                    style={{ width: '100%' }}
                    placeholder={['开始时间', '结束时间']}
                    onChange={handleDateRangeChange}
                  />
                </Form.Item>
              </Col>
              <Col span={COL_SPAN_FORM}>
                <Form.Item
                  label="MIS号"
                  style={{ width: '100%', marginBottom: `${FORM_MARGIN}px` }}
                >
                  <Input
                    value={queryForm.misNumber}
                    onChange={e => setQueryForm({ ...queryForm, misNumber: e.target.value })}
                    placeholder="请输入MIS号"
                  />
                </Form.Item>
              </Col>
              <Col span={COL_SPAN_FORM}>
                <Form.Item
                  label="PR ID"
                  style={{ width: '100%', marginBottom: `${FORM_MARGIN}px` }}
                >
                  <Input
                    value={queryForm.prId}
                    onChange={e => setQueryForm({ ...queryForm, prId: e.target.value })}
                    placeholder="请输入PR ID"
                  />
                </Form.Item>
              </Col>
              <Col span={COL_SPAN_FORM}>
                <Form.Item
                  label="仓库地址"
                  style={{ width: '100%', marginBottom: `${FORM_MARGIN}px` }}
                >
                  <Input
                    value={queryForm.gitRepository}
                    onChange={e => setQueryForm({ ...queryForm, gitRepository: e.target.value })}
                    placeholder="请输入仓库地址"
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={[ROW_GUTTER_VALUE, ROW_GUTTER_VALUE]} style={{ width: '100%' }}>
              <Col span={COL_SPAN_FORM}>
                <Form.Item label="分支" style={{ width: '100%', marginBottom: `${FORM_MARGIN}px` }}>
                  <Input
                    value={queryForm.gitBranch}
                    onChange={e => setQueryForm({ ...queryForm, gitBranch: e.target.value })}
                    placeholder="请输入分支名"
                  />
                </Form.Item>
              </Col>
              <Col span={COL_SPAN_BUTTONS} style={{ textAlign: 'right' }}>
                <Button
                  type="primary"
                  onClick={handleSearch}
                  loading={loading}
                  icon={<SearchOutlined />}
                  style={{
                    marginRight: `${BUTTON_MARGIN}px`,
                    borderRadius: '4px',
                  }}
                >
                  查询
                </Button>
                <Button
                  onClick={handleReset}
                  icon={<ReloadOutlined />}
                  style={{ borderRadius: '4px' }}
                >
                  重置
                </Button>
              </Col>
            </Row>
          </Form>

          <Table
            columns={columns}
            dataSource={data}
            rowKey={record => record.id.toString()}
            loading={loading}
            pagination={{
              current: currentPage,
              pageSize,
              total,
              showTotal: total => `共 ${total} 条记录`,
              onChange: handlePageChange,
              showSizeChanger: false,
              simple: optimizationConfig.tableConfig.virtual, // 低性能设备使用简单分页
              style: { marginTop: `${TABLE_MARGIN}px` },
            }}
            expandable={{
              expandedRowRender: record => (
                <Table
                  columns={fileColumns}
                  dataSource={record.fileDetails}
                  rowKey={file => file.id.toString()}
                  pagination={false}
                  size="small"
                  style={{ margin: `0 ${EXPANDED_TABLE_MARGIN}px` }}
                  scroll={optimizationConfig.tableConfig.virtual ? { y: 200 } : undefined}
                />
              ),
              expandRowByClick: true,
              expandedRowClassName: () => 'expanded-row',
            }}
            style={{
              backgroundColor: '#fff',
              borderRadius: '4px',
              overflow: 'hidden',
            }}
            rowClassName={(_, index) =>
              index % TABLE_ROW_INDEX_DIVISOR === 0 ? 'table-row-light' : 'table-row-dark'
            }
            scroll={optimizationConfig.tableConfig.virtual ? { y: 400 } : undefined}
            showSorterTooltip={optimizationConfig.tableConfig.showSorterTooltip}
          />
        </Card>
      </div>
    </ConfigProvider>
  )
}
/* eslint-enable camelcase */

export default AIAnalysis
