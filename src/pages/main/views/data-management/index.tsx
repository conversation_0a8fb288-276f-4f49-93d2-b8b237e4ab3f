import React, { useState, useEffect, useCallback, useMemo } from '@rome/stone/react'
import {
  Card,
  Table,
  Button,
  Input,
  Form,
  DatePicker,
  Modal,
  Row,
  Col,
  message,
  ConfigProvider,
} from 'antd'
import zhCN from 'antd/lib/locale/zh_CN'
import { SearchOutlined, ReloadOutlined, CopyOutlined, DatabaseOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import { post } from '@/lib/utils/request'
import { API_CONFIG } from '@/lib/config/api'
import { getOptimizationConfig } from '@/lib/utils/performance'

// 设置dayjs使用中文
dayjs.locale('zh-cn')

// 定义数据类型
interface CodeGenRecord {
  id: number
  status: number
  gitRepository: string
  gitBranch: string
  gitUsername: string
  ide: string
  generationTimestamp: number
  inputDesignData: string
  inputDescription: string
  inputApiMock: string
  userQuery: string
  generatedCode: string
}

interface QueryParams {
  createdAt?: string
  gitUsername?: string
  gitRepository?: string
  gitBranch?: string
  pageNum: number
  pageSize: number
}

interface ApiResponse {
  pageNum: number
  pageSize: number
  total: number
  pages: number
  list: CodeGenRecord[]
}

// 常量定义
const COL_SPAN_BASE = 16
const ROW_GUTTER_VALUE = 16
const COL_SPAN_CELL = 8
const COL_SPAN_DOUBLE = 16
const COL_SPAN_FULL = 24
const COL_SPAN_FORM = 6
const MODAL_TOP_OFFSET = 20

const DataManagement: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<CodeGenRecord[]>([])
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedRecord, setSelectedRecord] = useState<CodeGenRecord | null>(null)
  const [visible, setVisible] = useState(false)

  // 性能优化配置
  const optimizationConfig = useMemo(() => getOptimizationConfig(), [])
  const pageSize = optimizationConfig.tableConfig.pageSize

  // 查询表单状态
  const [queryForm, setQueryForm] = useState<Partial<QueryParams>>({
    createdAt: '',
    gitUsername: '',
    gitRepository: '',
    gitBranch: '',
  })

  // 获取数据
  const fetchData = useCallback(async (params: QueryParams) => {
    setLoading(true)
    try {
      const result: ApiResponse = await post(
        API_CONFIG.DATA_MANAGEMENT.QUERY_CODE_GEN_RECORDS,
        params,
      )
      setData(result.list || [])
      setTotal(result.total || 0)
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('获取数据失败:', error)
      message.error('获取数据失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }, [])

  // 处理查询
  const handleSearch = useCallback(() => {
    const params: QueryParams = {
      ...queryForm,
      pageNum: 1,
      pageSize,
    }
    setCurrentPage(1)
    fetchData(params)
  }, [queryForm, pageSize, fetchData])

  // 处理重置
  const handleReset = () => {
    setQueryForm({
      createdAt: '',
      gitUsername: '',
      gitRepository: '',
      gitBranch: '',
    })
    setCurrentPage(1)
    fetchData({
      pageNum: 1,
      pageSize,
    })
  }

  // 处理分页
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    fetchData({
      ...queryForm,
      pageNum: page,
      pageSize,
    })
  }

  // 查看详情
  const handleViewDetail = (record: CodeGenRecord) => {
    setSelectedRecord(record)
    setVisible(true)
  }

  // 格式化时间戳
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('zh-CN')
  }

  // 日期变更处理函数
  const handleDateChange = (_date: any, dateString: string | string[]) => {
    if (typeof dateString === 'string') {
      setQueryForm({ ...queryForm, createdAt: dateString })
    }
  }

  // 定义一个通用的复制函数
  const copyToClipboard = (text: string) => {
    if (text) {
      navigator.clipboard
        .writeText(text)
        .then(() => message.success('内容已复制到剪贴板'))
        .catch(() => message.error('复制失败，请手动复制'))
    }
  }

  // 定义表格列
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      align: 'center' as const,
    },
    {
      title: 'MIS号',
      dataIndex: 'gitUsername',
      key: 'gitUsername',
      width: 120,
    },
    {
      title: '仓库地址',
      dataIndex: 'gitRepository',
      key: 'gitRepository',
      width: 320,
      render: (text: string) => (
        <div style={{ wordBreak: 'break-all', whiteSpace: 'normal' }}>{text}</div>
      ),
    },
    {
      title: '分支',
      dataIndex: 'gitBranch',
      key: 'gitBranch',
      width: 160,
      render: (text: string) => (
        <div style={{ wordBreak: 'break-all', whiteSpace: 'normal' }}>{text}</div>
      ),
    },
    {
      title: 'IDE',
      dataIndex: 'ide',
      key: 'ide',
      width: 100,
    },
    {
      title: '生成时间',
      dataIndex: 'generationTimestamp',
      key: 'generationTimestamp',
      width: 180,
      render: (timestamp: number) => formatTimestamp(timestamp),
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      align: 'center' as const,
      render: (_: any, record: CodeGenRecord) => (
        <Button
          type="primary"
          size="small"
          onClick={() => handleViewDetail(record)}
          style={{ borderRadius: '4px' }}
        >
          查看详情
        </Button>
      ),
    },
  ]

  // 初始加载
  useEffect(() => {
    handleSearch()
  }, [handleSearch])

  return (
    <ConfigProvider locale={zhCN}>
      <div style={{ minHeight: 'calc(100vh - 64px)' }}>
        <Card
          title={
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <DatabaseOutlined style={{ marginRight: 8, color: '#667eea' }} />
              <span style={{ fontSize: '18px', fontWeight: 600, color: '#334155' }}>
                原始数据管理
              </span>
            </div>
          }
          bordered={false}
          style={{
            marginBottom: '24px',
          }}
        >
          <Form layout="inline" style={{ marginBottom: '24px' }}>
            <Row gutter={[ROW_GUTTER_VALUE, ROW_GUTTER_VALUE]} style={{ width: '100%' }}>
              <Col span={COL_SPAN_FORM}>
                <Form.Item label="创建时间" style={{ width: '100%', marginBottom: '8px' }}>
                  <DatePicker
                    showTime
                    style={{ width: '100%' }}
                    placeholder="请选择时间"
                    onChange={handleDateChange}
                  />
                </Form.Item>
              </Col>
              <Col span={COL_SPAN_FORM}>
                <Form.Item label="MIS号" style={{ width: '100%', marginBottom: '8px' }}>
                  <Input
                    value={queryForm.gitUsername}
                    onChange={e => setQueryForm({ ...queryForm, gitUsername: e.target.value })}
                    placeholder="请输入MIS号"
                  />
                </Form.Item>
              </Col>
              <Col span={COL_SPAN_FORM}>
                <Form.Item label="仓库地址" style={{ width: '100%', marginBottom: '8px' }}>
                  <Input
                    value={queryForm.gitRepository}
                    onChange={e => setQueryForm({ ...queryForm, gitRepository: e.target.value })}
                    placeholder="请输入仓库地址"
                  />
                </Form.Item>
              </Col>
              <Col span={COL_SPAN_FORM}>
                <Form.Item label="仓库分支" style={{ width: '100%', marginBottom: '8px' }}>
                  <Input
                    value={queryForm.gitBranch}
                    onChange={e => setQueryForm({ ...queryForm, gitBranch: e.target.value })}
                    placeholder="请输入仓库分支"
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row style={{ marginTop: '16px', width: '100%' }}>
              <Col span={COL_SPAN_FULL} style={{ textAlign: 'right' }}>
                <Button
                  type="primary"
                  onClick={handleSearch}
                  loading={loading}
                  icon={<SearchOutlined />}
                  style={{
                    marginRight: '8px',
                    borderRadius: '4px',
                    backgroundColor: '#1890ff',
                  }}
                >
                  查询
                </Button>
                <Button
                  onClick={handleReset}
                  icon={<ReloadOutlined />}
                  style={{ borderRadius: '4px' }}
                >
                  重置
                </Button>
              </Col>
            </Row>
          </Form>

          <Table
            columns={columns}
            dataSource={data}
            rowKey={record => record.generationTimestamp.toString()}
            loading={loading}
            pagination={{
              current: currentPage,
              pageSize,
              total,
              showTotal: total => `共 ${total} 条记录`,
              onChange: handlePageChange,
              showSizeChanger: false,
              simple: optimizationConfig.tableConfig.virtual, // 低性能设备使用简单分页
              style: { marginTop: '16px' },
            }}
            style={{
              backgroundColor: '#fff',
              borderRadius: '4px',
              overflow: 'hidden',
            }}
            rowClassName={(_, index) =>
              index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
            }
            scroll={optimizationConfig.tableConfig.virtual ? { y: 400 } : undefined}
            showSorterTooltip={optimizationConfig.tableConfig.showSorterTooltip}
          />
        </Card>

        <Modal
          title={<span style={{ fontWeight: 500, fontSize: '16px' }}>详情信息</span>}
          open={visible}
          onCancel={() => setVisible(false)}
          width={800}
          footer={[
            <Button key="close" onClick={() => setVisible(false)} style={{ borderRadius: '4px' }}>
              关闭
            </Button>,
          ]}
          bodyStyle={{ padding: '24px', maxHeight: '70vh', overflow: 'auto' }}
          style={{ top: MODAL_TOP_OFFSET }}
        >
          {selectedRecord && (
            <div>
              <Row gutter={[COL_SPAN_BASE, COL_SPAN_BASE]}>
                <Col span={8}>
                  <div style={{ marginBottom: '12px' }}>
                    <strong>MIS号：</strong> {selectedRecord.gitUsername}
                  </div>
                </Col>
                <Col span={8}>
                  <div style={{ marginBottom: '12px' }}>
                    <strong>IDE：</strong> {selectedRecord.ide}
                  </div>
                </Col>
                <Col span={COL_SPAN_DOUBLE}>
                  <div style={{ marginBottom: '12px', wordBreak: 'break-all' }}>
                    <strong>仓库地址：</strong> {selectedRecord.gitRepository}
                  </div>
                </Col>
                <Col span={COL_SPAN_CELL}>
                  <div style={{ marginBottom: '12px' }}>
                    <strong>分支：</strong> {selectedRecord.gitBranch}
                  </div>
                </Col>
                <Col span={COL_SPAN_FULL}>
                  <div style={{ marginBottom: '12px' }}>
                    <strong>生成时间：</strong>{' '}
                    {formatTimestamp(selectedRecord.generationTimestamp)}
                  </div>
                </Col>
              </Row>

              <div style={{ marginTop: '24px' }}>
                <Card
                  title="设计数据"
                  size="small"
                  style={{ marginBottom: '16px', borderRadius: '4px' }}
                  headStyle={{ backgroundColor: '#f6f8fa' }}
                  extra={
                    <Button
                      type="text"
                      size="small"
                      icon={<CopyOutlined />}
                      onClick={() => copyToClipboard(selectedRecord.inputDesignData || '')}
                    >
                      复制
                    </Button>
                  }
                >
                  <pre
                    style={{
                      maxHeight: '200px',
                      overflow: 'auto',
                      background: '#f5f5f5',
                      padding: '12px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      margin: 0,
                    }}
                  >
                    {selectedRecord.inputDesignData || '无'}
                  </pre>
                </Card>
              </div>

              <div style={{ marginTop: '16px' }}>
                <Card
                  title="描述信息"
                  size="small"
                  style={{ marginBottom: '16px', borderRadius: '4px' }}
                  headStyle={{ backgroundColor: '#f6f8fa' }}
                  extra={
                    <Button
                      type="text"
                      size="small"
                      icon={<CopyOutlined />}
                      onClick={() => copyToClipboard(selectedRecord.inputDescription || '')}
                    >
                      复制
                    </Button>
                  }
                >
                  <pre
                    style={{
                      maxHeight: '200px',
                      overflow: 'auto',
                      background: '#f5f5f5',
                      padding: '12px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      margin: 0,
                    }}
                  >
                    {selectedRecord.inputDescription || '无'}
                  </pre>
                </Card>
              </div>

              <div style={{ marginTop: '16px' }}>
                <Card
                  title="API Mock"
                  size="small"
                  style={{ marginBottom: '16px', borderRadius: '4px' }}
                  headStyle={{ backgroundColor: '#f6f8fa' }}
                  extra={
                    <Button
                      type="text"
                      size="small"
                      icon={<CopyOutlined />}
                      onClick={() => copyToClipboard(selectedRecord.inputApiMock || '')}
                    >
                      复制
                    </Button>
                  }
                >
                  <pre
                    style={{
                      maxHeight: '200px',
                      overflow: 'auto',
                      background: '#f5f5f5',
                      padding: '12px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      margin: 0,
                    }}
                  >
                    {selectedRecord.inputApiMock || '无'}
                  </pre>
                </Card>
              </div>

              <div style={{ marginTop: '16px' }}>
                <Card
                  title="用户查询"
                  size="small"
                  style={{ marginBottom: '16px', borderRadius: '4px' }}
                  headStyle={{ backgroundColor: '#f6f8fa' }}
                  extra={
                    <Button
                      type="text"
                      size="small"
                      icon={<CopyOutlined />}
                      onClick={() => copyToClipboard(selectedRecord.userQuery || '')}
                    >
                      复制
                    </Button>
                  }
                >
                  <pre
                    style={{
                      maxHeight: '200px',
                      overflow: 'auto',
                      background: '#f5f5f5',
                      padding: '12px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      margin: 0,
                    }}
                  >
                    {selectedRecord.userQuery || '无'}
                  </pre>
                </Card>
              </div>

              <div style={{ marginTop: '16px' }}>
                <Card
                  title="生成代码"
                  size="small"
                  style={{ marginBottom: '16px', borderRadius: '4px' }}
                  headStyle={{ backgroundColor: '#f6f8fa' }}
                  extra={
                    <Button
                      type="text"
                      size="small"
                      icon={<CopyOutlined />}
                      onClick={() => {
                        if (selectedRecord?.generatedCode) {
                          copyToClipboard(selectedRecord.generatedCode)
                        }
                      }}
                    >
                      复制代码
                    </Button>
                  }
                >
                  <pre
                    style={{
                      maxHeight: '300px',
                      overflow: 'auto',
                      background: '#f5f5f5',
                      padding: '12px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      margin: 0,
                    }}
                  >
                    {selectedRecord.generatedCode || '无'}
                  </pre>
                </Card>
              </div>
            </div>
          )}
        </Modal>
      </div>
    </ConfigProvider>
  )
}

export default DataManagement
