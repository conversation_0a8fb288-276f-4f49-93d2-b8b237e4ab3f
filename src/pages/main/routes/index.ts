import React from '@rome/stone/react'
import { Redirect } from '@rome/stone/react-router-dom'
import Home from '../views/home'
import DataManagement from '../views/data-management'
import AIAnalysis from '../views/ai-analysis'

export default [
  { path: '/main/home', component: Home, exact: true },
  { path: '/main/data-management', component: DataManagement, exact: true },
  { path: '/main/ai-analysis', component: AIAnalysis, exact: true },
  {
    path: '/',
    exact: true,
    render: (): JSX.Element => React.createElement(Redirect, { to: '/main/home' }),
  },
]
