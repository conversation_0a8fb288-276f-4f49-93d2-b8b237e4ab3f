.app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen', 'Ubuntu',
    '<PERSON><PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  min-height: 100vh;
}

.appHeader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  color: white;
  font-size: calc(10px + 2vmin);
  background-color: #282c34;
}

.appLogo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .appLogo {
    animation: App-logo-spin infinite 20s linear;
  }
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.sidebar {
  background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  border-right: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 1px;
    height: 100%;
    background: linear-gradient(180deg, transparent 0%, #e2e8f0 50%, transparent 100%);
  }
}

.sidebarHeader {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(14, 165, 233, 0.2);
  border-bottom: none;

  h3 {
    background: linear-gradient(45deg, #ffffff, #e2e8f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
    letter-spacing: 0.5px;
  }
}

.navItem {
  margin: 4px 12px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(14, 165, 233, 0.08), transparent);
    transition: left 0.5s;
  }

  &:hover {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    &::before {
      left: 100%;
    }
  }

  a {
    display: block;
    padding: 12px 16px;
    color: #475569;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s;

    &:hover {
      color: #334155;
    }
  }

  &.active {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
    color: white;
    box-shadow: 0 4px 16px rgba(14, 165, 233, 0.25);

    a {
      color: white !important;
      font-weight: 500;
    }
  }
}

.header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-bottom: none;
  backdrop-filter: blur(10px);

  h2 {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
    letter-spacing: 0.5px;
  }
}

.toggleButton {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(14, 165, 233, 0.2);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(14, 165, 233, 0.25);
  }

  &:active {
    transform: translateY(0);
  }
}

.headerButton {
  &:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 16px rgba(14, 165, 233, 0.3) !important;
  }

  &:active {
    transform: translateY(0) !important;
  }
}

.mainContent {
  background: transparent;
  padding: 24px;
}

.contentWrapper {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
}

.loading {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);

  .loadingSpinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #0ea5e9;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loadingText {
    margin-top: 16px;
    color: #475569;
    font-weight: 500;
    letter-spacing: 0.5px;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

:global {
  .ant-card {
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    border: 1px solid rgba(0, 0, 0, 0.06) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

    &:hover {
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;
      transform: translateY(-2px) !important;
    }

    .ant-card-head {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
      border-bottom: 1px solid #e2e8f0 !important;
      border-radius: 12px 12px 0 0 !important;
    }

    .ant-card-head-title {
      color: #334155 !important;
      font-weight: 600 !important;
    }
  }

  .ant-table {
    border-radius: 8px !important;
    overflow: hidden !important;

    .ant-table-thead > tr > th {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
      color: #475569 !important;
      font-weight: 600 !important;
      border-bottom: 2px solid #e2e8f0 !important;
    }

    .ant-table-tbody > tr:hover > td {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
    }
  }

  .ant-btn-primary {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%) !important;
    border: none !important;
    box-shadow: 0 2px 8px rgba(14, 165, 233, 0.2) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

    &:hover {
      transform: translateY(-2px) !important;
      box-shadow: 0 4px 16px rgba(14, 165, 233, 0.25) !important;
    }
  }

  .ant-input {
    border-radius: 8px !important;
    border: 1px solid #e2e8f0 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

    &:focus {
      border-color: #0ea5e9 !important;
      box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.08) !important;
    }
  }

  .ant-modal {
    .ant-modal-content {
      border-radius: 16px !important;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2) !important;
      backdrop-filter: blur(10px) !important;
    }

    .ant-modal-header {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
      border-bottom: 1px solid #e2e8f0 !important;
      border-radius: 16px 16px 0 0 !important;
    }
  }
}
