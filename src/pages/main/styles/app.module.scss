// 性能优化：检测是否支持硬件加速
@supports (transform: translateZ(0)) {
  .app {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu',
      'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    min-height: 100vh;
    // 启用硬件加速
    transform: translateZ(0);
    will-change: auto;
  }
}

// 降级方案：不支持硬件加速时使用简单背景
@supports not (transform: translateZ(0)) {
  .app {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu',
      'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background: #f5f5f5; // 简单的纯色背景
    min-height: 100vh;
  }
}

.appHeader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  color: white;
  font-size: calc(10px + 2vmin);
  background-color: #282c34;
}

.appLogo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .appLogo {
    animation: App-logo-spin infinite 20s linear;
  }
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 性能优化的侧边栏样式
@supports (transform: translateZ(0)) {
  .sidebar {
    background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
    border-right: none;
    transition: width 0.3s ease; // 只对width进行动画，减少重绘
    transform: translateZ(0); // 启用硬件加速
    will-change: width; // 明确指定变化属性

    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 1px;
      height: 100%;
      background: linear-gradient(180deg, transparent 0%, #e2e8f0 50%, transparent 100%);
    }
  }
}

// 降级方案：简化样式
@supports not (transform: translateZ(0)) {
  .sidebar {
    background: #ffffff;
    border-right: 1px solid #e2e8f0;
    transition: width 0.2s ease;
  }
}

.sidebarHeader {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(14, 165, 233, 0.2);
  border-bottom: none;

  h3 {
    background: linear-gradient(45deg, #ffffff, #e2e8f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
    letter-spacing: 0.5px;
  }
}

// 性能优化的导航项样式
@supports (transform: translateZ(0)) {
  .navItem {
    margin: 4px 12px;
    border-radius: 8px;
    transition: background-color 0.2s ease, transform 0.2s ease; // 简化动画
    position: relative;
    transform: translateZ(0); // 启用硬件加速
    will-change: background-color, transform;

    // 移除复杂的伪元素动画，减少重绘
    &:hover {
      background: #f1f5f9;
      transform: translateX(2px); // 减少移动距离
    }

    a {
      display: block;
      padding: 12px 16px;
      color: #475569;
      text-decoration: none;
      font-weight: 500;
      transition: color 0.2s ease;

      &:hover {
        color: #334155;
      }
    }

    &.active {
      background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
      color: white;
      box-shadow: 0 2px 8px rgba(14, 165, 233, 0.2); // 减少阴影强度

      a {
        color: white !important;
        font-weight: 500;
      }
    }
  }
}

// 降级方案：简化样式
@supports not (transform: translateZ(0)) {
  .navItem {
    margin: 4px 12px;
    border-radius: 8px;
    transition: background-color 0.2s ease;

    &:hover {
      background: #f1f5f9;
    }

    a {
      display: block;
      padding: 12px 16px;
      color: #475569;
      text-decoration: none;
      font-weight: 500;
    }

    &.active {
      background: #0ea5e9;
      color: white;

      a {
        color: white !important;
        font-weight: 500;
      }
    }
  }
}

.header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-bottom: none;
  backdrop-filter: blur(10px);

  h2 {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
    letter-spacing: 0.5px;
  }
}

.toggleButton {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(14, 165, 233, 0.2);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(14, 165, 233, 0.25);
  }

  &:active {
    transform: translateY(0);
  }
}

.headerButton {
  &:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 16px rgba(14, 165, 233, 0.3) !important;
  }

  &:active {
    transform: translateY(0) !important;
  }
}

.mainContent {
  background: transparent;
  padding: 24px;
}

.contentWrapper {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
}

.loading {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);

  .loadingSpinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #0ea5e9;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loadingText {
    margin-top: 16px;
    color: #475569;
    font-weight: 500;
    letter-spacing: 0.5px;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 性能优化的全局样式
:global {
  // 支持硬件加速时的样式
  @supports (transform: translateZ(0)) {
    .ant-card {
      border-radius: 12px !important;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important; // 减少阴影强度
      border: 1px solid rgba(0, 0, 0, 0.06) !important;
      transition: box-shadow 0.2s ease, transform 0.2s ease !important; // 简化动画
      transform: translateZ(0) !important; // 启用硬件加速
      will-change: box-shadow, transform !important;

      &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important; // 减少阴影强度
        transform: translateY(-1px) translateZ(0) !important; // 减少移动距离
      }

      .ant-card-head {
        background: #f8fafc !important; // 简化背景
        border-bottom: 1px solid #e2e8f0 !important;
        border-radius: 12px 12px 0 0 !important;
      }

      .ant-card-head-title {
        color: #334155 !important;
        font-weight: 600 !important;
      }
    }

    .ant-table {
      border-radius: 8px !important;
      overflow: hidden !important;

      .ant-table-thead > tr > th {
        background: #f8fafc !important; // 简化背景
        color: #475569 !important;
        font-weight: 600 !important;
        border-bottom: 1px solid #e2e8f0 !important; // 减少边框厚度
      }

      .ant-table-tbody > tr:hover > td {
        background: #f8fafc !important; // 简化背景
      }
    }

    .ant-btn-primary {
      background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%) !important;
      border: none !important;
      box-shadow: 0 1px 4px rgba(14, 165, 233, 0.15) !important; // 减少阴影
      transition: transform 0.2s ease, box-shadow 0.2s ease !important;
      transform: translateZ(0) !important;
      will-change: transform, box-shadow !important;

      &:hover {
        transform: translateY(-1px) translateZ(0) !important; // 减少移动距离
        box-shadow: 0 2px 8px rgba(14, 165, 233, 0.2) !important;
      }
    }

    .ant-input {
      border-radius: 8px !important;
      border: 1px solid #e2e8f0 !important;
      transition: border-color 0.2s ease, box-shadow 0.2s ease !important;

      &:focus {
        border-color: #0ea5e9 !important;
        box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.06) !important; // 减少阴影强度
      }
    }
  }

  // 不支持硬件加速时的降级样式
  @supports not (transform: translateZ(0)) {
    .ant-card {
      border-radius: 8px !important;
      border: 1px solid #e2e8f0 !important;
      transition: none !important; // 移除动画

      .ant-card-head {
        background: #f8fafc !important;
        border-bottom: 1px solid #e2e8f0 !important;
        border-radius: 8px 8px 0 0 !important;
      }

      .ant-card-head-title {
        color: #334155 !important;
        font-weight: 600 !important;
      }
    }

    .ant-table {
      border-radius: 4px !important;

      .ant-table-thead > tr > th {
        background: #f8fafc !important;
        color: #475569 !important;
        font-weight: 600 !important;
        border-bottom: 1px solid #e2e8f0 !important;
      }

      .ant-table-tbody > tr:hover > td {
        background: #f8fafc !important;
      }
    }

    .ant-btn-primary {
      background: #0ea5e9 !important;
      border: none !important;
      transition: none !important;
    }

    .ant-input {
      border-radius: 4px !important;
      border: 1px solid #e2e8f0 !important;
      transition: border-color 0.2s ease !important;

      &:focus {
        border-color: #0ea5e9 !important;
      }
    }
  }
}
