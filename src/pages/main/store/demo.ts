import { makeAutoObservable } from 'mobx'
// 这些装饰器在代码中被使用，但 ESLint 无法识别
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { model, loading } from '@nibfe/mobx-loading'
import { DemoState } from '../types/typings'

const initialState: DemoState = {
  count: 0,
}

@model('DemoStore')
class Store {
  constructor() {
    makeAutoObservable(this)
  }

  count = initialState.count

  setCount(value: number) {
    this.count = value
  }

  /** 异步请求 */
  @loading()
  async getCount() {
    const TIME = 1000
    const res: number = await new Promise(resolve => {
      setTimeout(() => {
        resolve(TIME)
      }, TIME)
    })
    this.setCount(res)
  }
}

export default new Store()
