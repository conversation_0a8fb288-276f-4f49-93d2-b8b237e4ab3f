import { makeAutoObservable } from 'mobx'
import { get } from '@/lib/utils/request'
import { API_CONFIG } from '@/lib/config/api'

// 数据类型定义
export interface DashboardOverview {
  totalPRs: number
  avgAIRatio: number
  activeRepos: number
  activeDevelopers: number
  trends: {
    prGrowth: number
    aiRatioGrowth: number
  }
}

export interface TrendData {
  date: string
  aiRatio: number
  prCount: number
}

export interface RankingItem {
  name: string
  fullName?: string
  aiRatio: number
  prCount?: number
  totalAILines?: number
  aiLines?: number
  totalLines?: number
}

export interface FileTypeItem {
  type: string
  aiRatio: number
  fileCount: number
  totalAILines: number
}

export interface DistributionItem {
  range?: string
  method?: string
  source?: string
  count: number
  accuracy?: number
  avgConfidence?: number
}

export interface ActivityItem {
  timestamp: string
  type: string
  prId: string
  prTitle: string
  aiRatio: number
  developer: string
  repository: string
}

export interface DashboardData {
  overview: DashboardOverview
  trends: {
    daily: TrendData[]
  }
  rankings: {
    repositories: RankingItem[]
    developers: RankingItem[]
    fileTypes: FileTypeItem[]
  }
  distributions: {
    aiRatioRanges: DistributionItem[]
    detectionMethods: DistributionItem[]
    aiSources: DistributionItem[]
  }
  recentActivities: ActivityItem[]
  metadata: {
    generatedAt: string
    timeRange: string
    filters: {
      repository: string | null
      developer: string | null
      fileType: string | null
    }
  }
}

export interface DashboardFilters {
  timeRange: string
  startDate?: string
  endDate?: string
  repository?: string
  developer?: string
  fileType?: string
}

class DashboardStore {
  constructor() {
    makeAutoObservable(this)
  }

  // 状态数据
  dashboardData: DashboardData | null = null

  filters: DashboardFilters = {
    timeRange: '30d',
  }

  // 设置筛选条件
  setFilters(filters: Partial<DashboardFilters>) {
    this.filters = { ...this.filters, ...filters }
  }

  // 重置筛选条件
  resetFilters() {
    this.filters = {
      timeRange: '30d',
    }
  }

  // 获取大盘数据
  async getDashboardData() {
    const params = new URLSearchParams()

    // 添加筛选参数
    Object.entries(this.filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString())
      }
    })

    const url = `${API_CONFIG.DASHBOARD.GET_DASHBOARD_DATA}?${params.toString()}`
    const response = await get<{ success: boolean; data: DashboardData; message: string }>(url)

    if (response.success) {
      this.dashboardData = response.data
    } else {
      throw new Error(response.message || '获取大盘数据失败')
    }
  }

  // 获取格式化的总览数据
  get formattedOverview() {
    if (!this.dashboardData?.overview) return null

    const { overview } = this.dashboardData
    return {
      totalPRs: {
        value: overview.totalPRs,
        growth: overview.trends.prGrowth,
        suffix: '',
      },
      avgAIRatio: {
        value: overview.avgAIRatio,
        growth: overview.trends.aiRatioGrowth,
        suffix: '%',
      },
      activeRepos: {
        value: overview.activeRepos,
        growth: 0, // API暂未提供
        suffix: '',
      },
      activeDevelopers: {
        value: overview.activeDevelopers,
        growth: 0, // API暂未提供
        suffix: '',
      },
    }
  }

  // 获取趋势图表数据
  get trendChartData() {
    if (!this.dashboardData?.trends.daily) return null

    return {
      dates: this.dashboardData.trends.daily.map(item => item.date),
      aiRatios: this.dashboardData.trends.daily.map(item => item.aiRatio),
      prCounts: this.dashboardData.trends.daily.map(item => item.prCount),
    }
  }

  // 获取文件类型分布数据
  get fileTypeDistribution() {
    return this.dashboardData?.rankings.fileTypes || []
  }

  // 获取仓库排行榜数据
  get repositoryRankings() {
    return this.dashboardData?.rankings.repositories || []
  }

  // 获取开发者排行榜数据
  get developerRankings() {
    return this.dashboardData?.rankings.developers || []
  }

  // 获取检测方法分布数据
  get detectionMethodsData() {
    return this.dashboardData?.distributions.detectionMethods || []
  }

  // 获取AI来源分布数据
  get aiSourcesData() {
    return this.dashboardData?.distributions.aiSources || []
  }

  // 获取最近活动数据
  get recentActivities() {
    return this.dashboardData?.recentActivities || []
  }
}

export default new DashboardStore()
