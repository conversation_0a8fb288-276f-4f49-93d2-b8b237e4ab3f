import React, { useState } from '@rome/stone/react'
import {
  HashRouter as Router,
  Switch,
  Route,
  Link,
  useLocation,
} from '@rome/stone/react-router-dom'
import { observer } from '@rome/stone/mobx-react'
import {
  DashboardOutlined,
  RobotOutlined,
  DatabaseOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BookOutlined,
  CustomerServiceOutlined,
} from '@ant-design/icons'
import { Tooltip } from 'antd'
import routes from './routes'
import styles from './styles/app.module.scss'

// 常量定义
const SIDEBAR_EXPANDED_WIDTH = 200
const SIDEBAR_COLLAPSED_WIDTH = 80
const HEADER_HEIGHT = 64
const ICON_MARGIN_RIGHT = 8

const Loading = ({ loading }: { loading: boolean }) => {
  return loading ? (
    <div
      className={styles.loading}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 9999,
      }}
    >
      <div className={styles.loadingSpinner} />
      <div className={styles.loadingText}>加载中...</div>
    </div>
  ) : null
}

const AppLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false)
  const location = useLocation()

  const handleTutorialClick = () => {
    window.open('https://km.sankuai.com/collabpage/2712867552', '_blank')
  }

  return (
    <div style={{ display: 'flex', minHeight: '100vh' }}>
      {/* 左侧导航 */}
      <div
        className={styles.sidebar}
        style={{
          width: collapsed ? SIDEBAR_COLLAPSED_WIDTH : SIDEBAR_EXPANDED_WIDTH,
          position: 'relative',
        }}
      >
        <div
          className={styles.sidebarHeader}
          style={{
            height: HEADER_HEIGHT,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <h3 style={{ margin: 0 }}>{collapsed ? 'AI' : 'AI Coding'}</h3>
        </div>
        <nav style={{ padding: '16px 0' }}>
          <div
            className={`${styles.navItem} ${
              location.pathname === '/main/home' ? styles.active : ''
            }`}
          >
            <Link to="/main/home">
              <DashboardOutlined
                style={{ marginRight: collapsed ? 0 : ICON_MARGIN_RIGHT, fontSize: '16px' }}
              />
              {!collapsed && '数据大盘'}
            </Link>
          </div>
          <div
            className={`${styles.navItem} ${
              location.pathname === '/main/ai-analysis' ? styles.active : ''
            }`}
          >
            <Link to="/main/ai-analysis">
              <RobotOutlined
                style={{ marginRight: collapsed ? 0 : ICON_MARGIN_RIGHT, fontSize: '16px' }}
              />
              {!collapsed && 'AI代码分析'}
            </Link>
          </div>
          <div
            className={`${styles.navItem} ${
              location.pathname === '/main/data-management' ? styles.active : ''
            }`}
          >
            <Link to="/main/data-management">
              <DatabaseOutlined
                style={{ marginRight: collapsed ? 0 : ICON_MARGIN_RIGHT, fontSize: '16px' }}
              />
              {!collapsed && '原始数据'}
            </Link>
          </div>
        </nav>
      </div>

      {/* 右侧内容区域 */}
      <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {/* 顶部Header */}
        <header
          className={styles.header}
          style={{
            height: HEADER_HEIGHT,
            padding: '0 16px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <button
              className={styles.toggleButton}
              onClick={() => setCollapsed(!collapsed)}
              style={{
                marginRight: 16,
              }}
            >
              {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              <span style={{ marginLeft: 8 }}>{collapsed ? '展开' : '收起'}</span>
            </button>
            <h2 style={{ margin: 0 }}>服务零售用户端AICoding</h2>
          </div>

          <div style={{ display: 'flex', gap: '12px' }}>
            <button
              className={styles.headerButton}
              onClick={handleTutorialClick}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                padding: '8px 16px',
                border: 'none',
                borderRadius: '6px',
                background: 'linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%)',
                color: 'white',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                boxShadow: '0 2px 8px rgba(14, 165, 233, 0.2)',
              }}
            >
              <BookOutlined />
              接入教程
            </button>

            <Tooltip
              title={
                <img
                  src="https://p1.meituan.net/joyplaystatic/df2566a41b81f04ff5324399d6477fb5156985.jpg"
                  alt="联系我们"
                  style={{
                    width: '150px',
                    height: 'auto',
                    borderRadius: '6px',
                    display: 'block',
                  }}
                />
              }
              placement="bottomRight"
              styles={{ root: { maxWidth: '165px' } }}
            >
              <button
                className={styles.headerButton}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  padding: '8px 16px',
                  border: 'none',
                  borderRadius: '6px',
                  background: 'linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%)',
                  color: 'white',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '500',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  boxShadow: '0 2px 8px rgba(14, 165, 233, 0.2)',
                }}
              >
                <CustomerServiceOutlined />
                联系我们
              </button>
            </Tooltip>
          </div>
        </header>

        {/* 内容区域 */}
        <main
          className={styles.mainContent}
          style={{
            flex: 1,
            overflow: 'auto',
          }}
        >
          <div
            className={styles.contentWrapper}
            style={{
              padding: '24px',
              minHeight: `calc(100vh - ${HEADER_HEIGHT}px - 48px)`,
            }}
          >
            <Switch>
              {routes.map(route => (
                <Route key={route.path} {...route} />
              ))}
            </Switch>
          </div>
        </main>
      </div>
    </div>
  )
}

const App: React.FC = observer(() => {
  return (
    <div className={styles.app}>
      <Loading loading={false} />
      <Router>
        <AppLayout />
      </Router>
    </div>
  )
})

export default App
