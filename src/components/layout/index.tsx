import React from 'react'
import { Layout as AntdLayout } from 'antd'

const { Header, Content, Sider, Footer } = AntdLayout

interface LayoutProps {
  children?: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <AntdLayout style={{ minHeight: '100vh' }}>
      <Header style={{ background: '#fff', padding: '0 16px' }}>
        <div style={{ color: '#1890ff', fontSize: '18px', fontWeight: 'bold' }}>
          AI Coding 管理系统
        </div>
      </Header>
      <AntdLayout>
        <Sider width={200} style={{ background: '#fff' }}>
          {/* 这里可以添加菜单组件 */}
        </Sider>
        <Content style={{ padding: '16px', background: '#f0f2f5' }}>{children}</Content>
      </AntdLayout>
      <Footer style={{ textAlign: 'center', padding: '12px' }}>AI Coding 管理系统 ©2025</Footer>
    </AntdLayout>
  )
}

export default Layout
