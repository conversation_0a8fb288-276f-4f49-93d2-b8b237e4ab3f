import React, { useEffect, useState } from 'react'
import { Card, Progress, Typography, Space, Tag, Alert, Tooltip } from 'antd'
import {
  getPerformanceInfo,
  getMemoryUsage,
  isMac2019,
  type PerformanceInfo,
} from '@/lib/utils/performance'

const { Text, Title } = Typography

// 常量定义
const PERCENTAGE_MULTIPLIER = 100

interface PerformanceMonitorProps {
  visible?: boolean
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({ visible = false }) => {
  const [perfInfo, setPerfInfo] = useState<PerformanceInfo | null>(null)
  const [memoryInfo, setMemoryInfo] = useState<any>(null)
  const [frameRate, setFrameRate] = useState<number>(0)

  useEffect(() => {
    // 获取性能信息
    const info = getPerformanceInfo()
    setPerfInfo(info)

    // 获取内存信息
    const memory = getMemoryUsage()
    setMemoryInfo(memory)

    // 监控帧率
    let frameCount = 0
    let lastTime = performance.now()

    const measureFrameRate = (): void => {
      frameCount += 1
      const currentTime = performance.now()

      const FRAME_MEASURE_INTERVAL = 1000
      if (currentTime - lastTime >= FRAME_MEASURE_INTERVAL) {
        setFrameRate(frameCount)
        frameCount = 0
        lastTime = currentTime
      }

      requestAnimationFrame(measureFrameRate)
    }

    const rafId = requestAnimationFrame(measureFrameRate)

    return () => {
      cancelAnimationFrame(rafId)
    }
  }, [])

  if (!visible || !perfInfo) {
    return null
  }

  const getPerformanceLevel = (): { level: string; color: string } => {
    if (perfInfo.isLowPerformance) return { level: '低', color: 'error' }
    if (!perfInfo.hasHardwareAcceleration) return { level: '中', color: 'warning' }
    return { level: '高', color: 'success' }
  }

  const performanceLevel = getPerformanceLevel()

  return (
    <div
      style={{
        position: 'fixed',
        top: 20,
        right: 20,
        width: 320,
        zIndex: 9999,
        opacity: 0.9,
      }}
    >
      <Card
        title={
          <Space>
            <Title level={5} style={{ margin: 0 }}>
              性能监控
            </Title>
            <Tag color={performanceLevel.color}>{performanceLevel.level}性能</Tag>
          </Space>
        }
        size="small"
        style={{ fontSize: '12px' }}
      >
        {/* 设备检测 */}
        {isMac2019() && (
          <Alert
            message="检测到可能的19款Mac设备"
            description="已启用性能优化模式"
            type="info"
            showIcon
            style={{ marginBottom: 12, fontSize: '11px' }}
          />
        )}

        {/* 硬件信息 */}
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          <div>
            <Text strong>硬件加速: </Text>
            <Tag color={perfInfo.hasHardwareAcceleration ? 'success' : 'error'}>
              {perfInfo.hasHardwareAcceleration ? '已启用' : '未启用'}
            </Tag>
          </div>

          <div>
            <Text strong>设备内存: </Text>
            <Tooltip
              title={
                <div>
                  <div>API报告: {perfInfo.deviceMemory.reported}GB</div>
                  <div>性能估算: {perfInfo.deviceMemory.estimated}GB</div>
                  <div>置信度: {perfInfo.deviceMemory.confidence}</div>
                </div>
              }
            >
              <Text style={{ cursor: 'help', borderBottom: '1px dashed #ccc' }}>
                {perfInfo.deviceMemory.estimated}GB
                <Tag
                  size="small"
                  color={(() => {
                    if (perfInfo.deviceMemory.confidence === 'high') return 'green'
                    if (perfInfo.deviceMemory.confidence === 'medium') return 'orange'
                    return 'red'
                  })()}
                  style={{ marginLeft: 4 }}
                >
                  {perfInfo.deviceMemory.confidence}
                </Tag>
              </Text>
            </Tooltip>
          </div>

          <div>
            <Text strong>CPU核心: </Text>
            <Text>{perfInfo.hardwareConcurrency}核</Text>
          </div>

          <div>
            <Text strong>网络类型: </Text>
            <Text>{perfInfo.connectionType}</Text>
          </div>

          {/* 帧率监控 */}
          <div>
            <Text strong>帧率: </Text>
            <Text style={{ color: (() => {
              const LOW_FPS_THRESHOLD = 30
              const MEDIUM_FPS_THRESHOLD = 50
              if (frameRate < LOW_FPS_THRESHOLD) return '#ff4d4f'
              if (frameRate < MEDIUM_FPS_THRESHOLD) return '#faad14'
              return '#52c41a'
            })() }}>
              {frameRate} FPS
            </Text>
          </div>

          {/* 内存使用 */}
          {memoryInfo && (
            <div>
              <Text strong>内存使用: </Text>
              <Progress
                percent={Math.round((memoryInfo.used / memoryInfo.limit) * PERCENTAGE_MULTIPLIER)}
                size="small"
                format={() => `${memoryInfo.used}MB / ${memoryInfo.limit}MB`}
                strokeColor={(() => {
                  const HIGH_MEMORY_THRESHOLD = 0.8
                  const MEDIUM_MEMORY_THRESHOLD = 0.6
                  const memoryRatio = memoryInfo.used / memoryInfo.limit
                  if (memoryRatio > HIGH_MEMORY_THRESHOLD) return '#ff4d4f'
                  if (memoryRatio > MEDIUM_MEMORY_THRESHOLD) return '#faad14'
                  return '#52c41a'
                })()}
              />
            </div>
          )}

          {/* 优化状态 */}
          <div style={{ marginTop: 8 }}>
            <Text strong>优化状态:</Text>
            <div style={{ marginTop: 4 }}>
              <Tag size="small" color={perfInfo.shouldUseSimpleAnimations ? 'orange' : 'green'}>
                {perfInfo.shouldUseSimpleAnimations ? '简化动画' : '完整动画'}
              </Tag>
              <Tag size="small" color={perfInfo.shouldReduceEffects ? 'orange' : 'green'}>
                {perfInfo.shouldReduceEffects ? '减少特效' : '完整特效'}
              </Tag>
              <Tag size="small" color={perfInfo.shouldOptimizeCharts ? 'orange' : 'green'}>
                {perfInfo.shouldOptimizeCharts ? '图表优化' : '标准图表'}
              </Tag>
            </div>
          </div>
        </Space>
      </Card>
    </div>
  )
}

export default PerformanceMonitor
