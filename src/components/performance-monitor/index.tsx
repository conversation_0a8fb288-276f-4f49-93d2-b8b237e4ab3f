import React, { useEffect, useState } from 'react'
import { Card, Progress, Typography, Space, Tag, Alert } from 'antd'
import { 
  getPerformanceInfo, 
  getMemoryUsage, 
  isMac2019,
  type PerformanceInfo 
} from '@/lib/utils/performance'

const { Text, Title } = Typography

interface PerformanceMonitorProps {
  visible?: boolean
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({ visible = false }) => {
  const [perfInfo, setPerfInfo] = useState<PerformanceInfo | null>(null)
  const [memoryInfo, setMemoryInfo] = useState<any>(null)
  const [frameRate, setFrameRate] = useState<number>(0)

  useEffect(() => {
    // 获取性能信息
    const info = getPerformanceInfo()
    setPerfInfo(info)

    // 获取内存信息
    const memory = getMemoryUsage()
    setMemoryInfo(memory)

    // 监控帧率
    let frameCount = 0
    let lastTime = performance.now()
    
    const measureFrameRate = () => {
      frameCount++
      const currentTime = performance.now()
      
      if (currentTime - lastTime >= 1000) {
        setFrameRate(frameCount)
        frameCount = 0
        lastTime = currentTime
      }
      
      requestAnimationFrame(measureFrameRate)
    }
    
    const rafId = requestAnimationFrame(measureFrameRate)

    return () => {
      cancelAnimationFrame(rafId)
    }
  }, [])

  if (!visible || !perfInfo) {
    return null
  }

  const getPerformanceLevel = () => {
    if (perfInfo.isLowPerformance) return { level: '低', color: 'error' }
    if (!perfInfo.hasHardwareAcceleration) return { level: '中', color: 'warning' }
    return { level: '高', color: 'success' }
  }

  const performanceLevel = getPerformanceLevel()

  return (
    <div
      style={{
        position: 'fixed',
        top: 20,
        right: 20,
        width: 320,
        zIndex: 9999,
        opacity: 0.9,
      }}
    >
      <Card
        title={
          <Space>
            <Title level={5} style={{ margin: 0 }}>
              性能监控
            </Title>
            <Tag color={performanceLevel.color}>{performanceLevel.level}性能</Tag>
          </Space>
        }
        size="small"
        style={{ fontSize: '12px' }}
      >
        {/* 设备检测 */}
        {isMac2019() && (
          <Alert
            message="检测到可能的19款Mac设备"
            description="已启用性能优化模式"
            type="info"
            showIcon
            style={{ marginBottom: 12, fontSize: '11px' }}
          />
        )}

        {/* 硬件信息 */}
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          <div>
            <Text strong>硬件加速: </Text>
            <Tag color={perfInfo.hasHardwareAcceleration ? 'success' : 'error'}>
              {perfInfo.hasHardwareAcceleration ? '已启用' : '未启用'}
            </Tag>
          </div>

          <div>
            <Text strong>设备内存: </Text>
            <Text>{perfInfo.deviceMemory}GB</Text>
          </div>

          <div>
            <Text strong>CPU核心: </Text>
            <Text>{perfInfo.hardwareConcurrency}核</Text>
          </div>

          <div>
            <Text strong>网络类型: </Text>
            <Text>{perfInfo.connectionType}</Text>
          </div>

          {/* 帧率监控 */}
          <div>
            <Text strong>帧率: </Text>
            <Text style={{ color: frameRate < 30 ? '#ff4d4f' : frameRate < 50 ? '#faad14' : '#52c41a' }}>
              {frameRate} FPS
            </Text>
          </div>

          {/* 内存使用 */}
          {memoryInfo && (
            <div>
              <Text strong>内存使用: </Text>
              <Progress
                percent={Math.round((memoryInfo.used / memoryInfo.limit) * 100)}
                size="small"
                format={() => `${memoryInfo.used}MB / ${memoryInfo.limit}MB`}
                strokeColor={
                  memoryInfo.used / memoryInfo.limit > 0.8 ? '#ff4d4f' : 
                  memoryInfo.used / memoryInfo.limit > 0.6 ? '#faad14' : '#52c41a'
                }
              />
            </div>
          )}

          {/* 优化状态 */}
          <div style={{ marginTop: 8 }}>
            <Text strong>优化状态:</Text>
            <div style={{ marginTop: 4 }}>
              <Tag size="small" color={perfInfo.shouldUseSimpleAnimations ? 'orange' : 'green'}>
                {perfInfo.shouldUseSimpleAnimations ? '简化动画' : '完整动画'}
              </Tag>
              <Tag size="small" color={perfInfo.shouldReduceEffects ? 'orange' : 'green'}>
                {perfInfo.shouldReduceEffects ? '减少特效' : '完整特效'}
              </Tag>
              <Tag size="small" color={perfInfo.shouldOptimizeCharts ? 'orange' : 'green'}>
                {perfInfo.shouldOptimizeCharts ? '图表优化' : '标准图表'}
              </Tag>
            </div>
          </div>
        </Space>
      </Card>
    </div>
  )
}

export default PerformanceMonitor
