# test mode - NODE_ENV 必须为 production - 对应 Talos 测试环境(test01 ~ test08 + newtest) - 判断当前环境请使用 APP_ENV
# https://cli.vuejs.org/zh/guide/mode-and-env.html
NODE_ENV=production
GENERATE_SOURCEMAP=true

APP_ENV=test

# 🐞REPLACE🐞 请替换成业务自己的APP_KEY
# 📖DOC📖 申请地址：https://camel.mws.sankuai.com/#/home

APP_KEY=undefined

# epassport 测试环境配置字段
APP_EPASSPORT_ENV=test


# 🐞REPLACE🐞 sso配置字段, 使用请替换业务自己的
# 📖DOC📖 申请clientId：https://km.sankuai.com/page/*********

APP_SSO_CLIENT_ID=2893258a16
APP_SSO_ENV=test

# ELINK APP KEY（必填）要在 SC 上可查
# 🐞REPLACE🐞 请替换成业务自己的APP_KEY
# 📖DOC📖 申请地址：https://camel.mws.sankuai.com/#/home
APP_ELINK_KEY=com.sankuai.ecom.rome.react
VUE_APP_KEY=undefined