#  my-react-app

## 使用方法
<!-- 如果项目为静态页面，建议描述地址、页面入口等信息. eg: -->
页面地址：
- staging: <xxx>
- production: <xxx>

<!-- 如果是APP内嵌页，建议补充描述页面进入路径。有鉴权的提示鉴权方法. eg: -->

### 本地开发
<!-- 主要描述项目本地开发需要的环境、启动命令等信息 -->
### 环境依赖

node v8.0+
依赖安装： `pnpm install`

### 项目构建
```
pnpm run build
```
### 启动项目

```
pnpm start
```

### 代码Lint
```
pnpm run lint
```
## 备注

<!-- 可以记录一些重要的项目信息，如：常见问题、目录结构、文档等，eg: -->

- 后端APP KEY: 
- owl key: 
- Talos发布地址: 
- 灵犀埋点信息:
