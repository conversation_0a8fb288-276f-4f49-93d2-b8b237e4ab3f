#!/usr/bin/env node

const path = require('path')
const cwd = path.resolve(__dirname, '../')
const batchBuild = require('@nibfe/vue-cli-plugin-project-build/lib/batchAsyncBuild.js')

// 1. check PUBLIC_URL
const publicUrl = process.env.PUBLIC_URL
if (!publicUrl) {
  if (process.env.CI === 'true') {
    console.error('缺失环境变量 PUBLIC_URL，请检查')
    process.exit(1)
  } else {
    console.warn('缺失环境变量 PUBLIC_URL 可能是本地构建，可执行 PUBLIC_URL=//awp-assets.sankuai.com/key/slug/ yarn build 模拟 Talos 线上构建，详情请查看 `https://sky.sankuai.com/docs/hfe/delivery/quickStart/go.html`')
  }
}

// 2. 数据准备
// 额外参数
const extraArgv = process.argv.slice(2)
// 获取 Talos 发布环境 target 默认按照 production 构建
const env = process.env.AWP_DEPLOY_ENV || 'production'
// 调整 mode，对应 .env.${production,staging,testxx}
const mode = process.env.VUE_CLI_MODE || (env === 'newtest' || env.startsWith('test0') ? 'test' : env)

// 是否启用 webpack analyze，支持 ANALYZE 或 --report 启用
const ANALYZE = !!process.env.ANALYZE && !extraArgv.includes('--report')

// 3. 显示将要执行的命令
console.log(`running: rome build --mode ${mode}${ANALYZE ? ' --report ' : ''}${extraArgv.join(' ')}`)

// 4. 执行命令，并透传其他 build 参数
const cmdParam = ['build', '--mode', mode, ANALYZE ? '--report' : '', ...extraArgv]
const nodeParam = { cwd, stdio: 'inherit' }
batchBuild({ command: 'rome', cmdParam, nodeParam })
