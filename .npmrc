save-exact=true
registry=http://r.npm.sankuai.com
disturl=http://npm.sankuai.com/dist/node
sass_binary_site=http://npm.sankuai.com/dist/node-sass
profiler_binary_host_mirror=https://npm.taobao.org/mirrors/node-inspector/
fse_binary_host_mirror=https://npm.taobao.org/mirrors/fsevents/

public-hoist-pattern[]=@types/react
public-hoist-pattern[]=@nibfe/vue-cli-plugin-project-build
public-hoist-pattern[]=@nibfe/axios-wrapper
public-hoist-pattern[]=vite
public-hoist-pattern[]=tslib
public-hoist-pattern[]=axios
public-hoist-pattern[]=regenerator-runtime
public-hoist-pattern[]=*-loader
public-hoist-pattern[]=*babel*
public-hoist-pattern[]=react*
public-hoist-pattern[]=mobx*
public-hoist-pattern[]=@types/*
public-hoist-pattern[]=@hfe/*
public-hoist-pattern[]=@ss*
public-hoist-pattern[]=@dp*
public-hoist-pattern[]=@mtfe/*
public-hoist-pattern[]=*commitlint*
public-hoist-pattern[]=prettier
public-hoist-pattern[]=@nibfe/*
public-hoist-pattern[]=@nibfe/eslint-plugin-todo-ddl
public-hoist-pattern[]=*eslint*
public-hoist-pattern[]=react
public-hoist-pattern[]=lodash
public-hoist-pattern[]=react-dom
public-hoist-pattern[]=react-router
public-hoist-pattern[]=react-router-dom
public-hoist-pattern[]=cropperjs
public-hoist-pattern[]=moment
public-hoist-pattern[]=mobx
public-hoist-pattern[]=@mtfe/sso-web
public-hoist-pattern[]=lint-staged
public-hoist-pattern[]=webpack
public-hoist-pattern[]=@rome/core
public-hoist-pattern[]=path-browserify
public-hoist-pattern[]=stream-browserify
public-hoist-pattern[]=querystring-es3
public-hoist-pattern[]=style-loader
public-hoist-pattern[]=process
public-hoist-pattern[]=vue-template-compiler
public-hoist-pattern[]=commitizen
public-hoist-pattern[]=cz-customizable
public-hoist-pattern[]=stylelint
public-hoist-pattern[]=@we*
public-hoist-pattern[]=@rome/*
public-hoist-pattern[]=@nibfe/react-cli-plugin-stone
public-hoist-pattern[]=@ss/mtd-react
public-hoist-pattern[]=mobx-react