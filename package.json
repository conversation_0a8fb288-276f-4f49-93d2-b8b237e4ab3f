{"name": "@gfe/ai-coding-admin", "version": "0.1.0", "private": true, "scripts": {"serve": "rome serve", "build": "node ./bin/talos_build --no-module", "test": "echo missing test", "lint": "rome romeLint --mode production", "check": "rome projectCheck", "commit": "git cz", "cov": "echo missing coverage", "lint:style": "rome lint:style", "newbranch": "npx git newbranch", "rome": "rome", "serve:https": "rome serve --https", "vite:serve": "rome exec vite --https --mode=development --config", "preinstall": "npx only-allow pnpm"}, "dependencies": {"@ant-design/icons": "6.0.0", "@nibfe/elink-sdk-web": "^0.0.10", "@nibfe/mobx-loading": "^1.0.0", "@nibfe/rome-cli-plugin-elink": "^1.0.0", "@nibfe/rome-cli-plugin-mobile-viewport": "^1.0.0", "antd": "5.25.2", "core-js": "^3.8.1", "dayjs": "1.11.13", "echarts": "5.6.0"}, "devDependencies": {"@nibfe/eslint-plugin-elink-scan": "^1.0.0", "@nibfe/preset-react": "3.1.0", "@nibfe/react-cli-plugin-generator": "latest", "@nibfe/rome-cli-plugin-micro-generator": "^0.0.2", "@nibfe/rome-react-fabric": "^2.1.4", "@rome/bundler-cli": "^1.0.5", "@rome/cli-plugin-stone": "^1.2.0", "@rome/cli-plugin-vite": "^0.1.3", "@rome/rome": "^1.1.1", "tslib": "2.4.1", "typescript": "^4.3", "yorkie-pnpm": "latest"}, "resolutions": {"eslint-plugin-import": "2.22.1", "eslint": "7.25.0"}, "prettier": "@nibfe/prettier-config", "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions"], "commitlint": {"extends": ["@nibfe/rome-react-fabric/lib/commitlint"]}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}, "cz-customizable": {"config": "./node_modules/@nibfe/rome-react-fabric/lib/czconfig"}}, "gitHooks": {"pre-commit": "lint-staged && node ./node_modules/@nibfe/eslint-plugin-elink-scan/check.js && pnpm run check", "commit-msg": "commitlint -e $GIT_PARAMS", "pre-push": "branchlint"}, "lint-staged": {"src/**/*.{css,scss}": "pnpm lint:style", "*.{js,jsx,ts,tsx,html}": ["pnpm run lint", "git add"], "src/**/*.{ts,tsx,js,jsx,json,css,scss,less,stylus}": ["prettier --write", "git add"]}, "stylelint": {"extends": "@nibfe/rome-react-fabric/lib/stylelint"}, "eslintConfig": {"extends": "./node_modules/@nibfe/rome-react-fabric/lib/eslint"}, "pnpm": {"overrides": {"@types/react": "17.0.2", "@mtfe/sso-web": "2.4.1"}}, "engines": {"pnpm": "^8.9.0", "node": ">=16.9.0"}, "packageManager": "pnpm@8.9.0"}