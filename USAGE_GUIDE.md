# AI Coding Admin 性能优化使用指南

## 快速开始

### 1. 开发环境验证

启动开发服务器后，在浏览器中访问应用：

```bash
pnpm serve
```

在开发环境中，您会在页面右上角看到性能监控面板，显示：
- 设备性能等级（高/中/低）
- 硬件加速状态
- 内存使用情况
- 实时帧率
- 当前优化配置

### 2. 19款Mac测试

在19款Mac上使用Chrome浏览器测试：

1. **禁用硬件加速**：
   - 打开Chrome设置
   - 搜索"硬件加速"
   - 关闭"可用时使用硬件加速"
   - 重启浏览器

2. **访问应用**：
   - 打开应用后观察性能监控面板
   - 应该显示"低性能"或"中性能"等级
   - 硬件加速状态显示为"未启用"

3. **验证优化效果**：
   - 页面滚动应该流畅
   - 图表动画简化但仍然可用
   - 表格操作响应迅速
   - 整体界面无明显卡顿

## 性能优化特性

### 自动检测和适配

系统会自动检测以下设备特性：
- 硬件加速支持
- 设备内存大小
- CPU核心数
- 网络连接类型
- 浏览器性能

根据检测结果自动应用相应的优化策略。

### 优化级别

#### 高性能设备
- 完整的视觉效果
- 流畅的动画
- 高质量图表渲染
- 标准表格功能

#### 中等性能设备
- 简化动画效果
- 优化图表配置
- 适中的分页大小
- 保留核心功能

#### 低性能设备
- 最小化动画
- 启用虚拟滚动
- 简化分页器
- 渐进式渲染

### 具体优化内容

#### CSS样式优化
- 自动检测硬件加速支持
- 条件性应用复杂特效
- 简化动画时长和缓动
- 优化阴影和渐变效果

#### JavaScript性能优化
- React组件渲染优化
- 防抖节流事件处理
- 内存泄漏预防
- 异步操作优化

#### 图表优化
- ECharts配置动态调整
- 渐进式渲染
- 动画效果控制
- 数据量优化

#### 表格优化
- 虚拟滚动启用
- 分页大小调整
- 排序功能优化
- 展开功能优化

## 开发指南

### 添加新的性能优化

1. **检测新的性能指标**：
```typescript
// 在 src/lib/utils/performance.ts 中添加
export const checkNewFeature = (): boolean => {
  // 实现检测逻辑
  return true
}
```

2. **更新优化配置**：
```typescript
// 在 getOptimizationConfig 函数中添加
const config = {
  // 现有配置...
  newFeatureConfig: {
    enabled: checkNewFeature(),
    // 其他配置项
  }
}
```

3. **在组件中使用**：
```typescript
const optimizationConfig = useMemo(() => getOptimizationConfig(), [])

// 根据配置调整组件行为
if (optimizationConfig.newFeatureConfig.enabled) {
  // 启用新特性
}
```

### 性能监控扩展

在 `src/components/performance-monitor/index.tsx` 中添加新的监控指标：

```typescript
const [newMetric, setNewMetric] = useState<number>(0)

useEffect(() => {
  // 监控新指标
  const measureNewMetric = () => {
    // 测量逻辑
    setNewMetric(value)
  }
  
  const interval = setInterval(measureNewMetric, 1000)
  return () => clearInterval(interval)
}, [])
```

### 样式优化扩展

在 `src/pages/main/styles/app.module.scss` 中添加新的条件样式：

```scss
// 检测新特性支持
@supports (new-css-feature: value) {
  .component {
    // 支持时的样式
  }
}

@supports not (new-css-feature: value) {
  .component {
    // 不支持时的降级样式
  }
}
```

## 故障排除

### 常见问题

1. **性能监控面板不显示**
   - 确认在开发环境中运行
   - 检查 `localhost` 域名访问
   - 查看浏览器控制台错误

2. **优化效果不明显**
   - 检查设备性能检测结果
   - 确认硬件加速状态
   - 验证优化配置是否正确应用

3. **图表渲染异常**
   - 检查ECharts配置
   - 确认数据格式正确
   - 查看性能优化配置

### 调试技巧

1. **查看性能信息**：
```javascript
// 在浏览器控制台中执行
import { getPerformanceInfo } from './src/lib/utils/performance'
console.log(getPerformanceInfo())
```

2. **监控内存使用**：
```javascript
// 查看内存使用情况
import { getMemoryUsage } from './src/lib/utils/performance'
console.log(getMemoryUsage())
```

3. **测试性能函数**：
```javascript
// 测试防抖函数
import { debounce } from './src/lib/utils/performance'
const debouncedFn = debounce(() => console.log('执行'), 300)
```

## 最佳实践

### 开发建议

1. **始终考虑性能影响**
   - 新增功能时评估性能开销
   - 使用性能监控验证效果
   - 为低性能设备提供降级方案

2. **合理使用优化配置**
   - 不要过度优化影响用户体验
   - 保持功能完整性
   - 测试各种设备和浏览器

3. **监控和测试**
   - 定期检查性能指标
   - 在不同设备上测试
   - 收集用户反馈

### 部署注意事项

1. **生产环境配置**
   - 性能监控组件不会在生产环境显示
   - 优化策略自动应用
   - 错误日志正常记录

2. **浏览器兼容性**
   - 支持所有现代浏览器
   - 自动降级到兼容模式
   - 保持功能可用性

3. **性能监控**
   - 可以集成APM工具
   - 收集真实用户性能数据
   - 持续优化改进
